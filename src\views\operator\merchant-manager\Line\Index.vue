<template>
  <div>
    <!--搜索区块-->
    <div class="tw-flex tw-flex-col tw-p-[16px]">
      <!--输入框容器-->
      <div class="tw-grid tw-grid-cols-5 tw-gap-[8px]">
        <el-input v-model.trim="searchForm.lineName" placeholder="线路名称" :icon="Search" clearable />
        <el-input v-model.trim="searchForm.lineNumber" placeholder="线路编号" :icon="Search" clearable />
        <el-select v-model.trim="searchForm.enableStatus" clearable placeholder="线路状态">
          <el-option
            v-for="lineStatusItem in enum2Options(MerchantLineEnableStatusEnum)"
            :key="lineStatusItem.value"
            :value="lineStatusItem.value"
            :label="lineStatusItem.name"
          />
        </el-select>
        <el-cascader
          v-model="searchForm.secondIndustries"
          :options="industryOptions"
          :props="industryProps"
          :show-all-levels="false"
          collapse-tags
          collapse-tags-tooltip
          placeholder="适用行业"
          clearable
        />
        <div class="tw-flex tw-justify-end">
          <el-button type="primary" link @click="clickLineResetFilter">
            <el-icon size="--el-font-size-base">
              <SvgIcon name="reset" color="var(--el-color-primary)" />
            </el-icon>
            <span>重置</span>
          </el-button>
          <el-button type="primary" link @click="clickLineSearch">
            <el-icon size="--el-font-size-base">
              <SvgIcon name="search" color="var(--el-color-primary)" />
            </el-icon>
            <span>搜索</span>
          </el-button>
        </div>
      </div>

      <!--创建线路按钮-->
      <div v-if="!!merchantStore.currentAccount?.accountEnable" class="tw-flex tw-justify-end tw-mt-[16px] tw-gap-x-1">
        <el-button type="danger" @click="batchStopLine">批量停用</el-button>
        
        <el-dropdown placement="top" trigger="click">
          <el-button type="primary" :icon="Plus">
            新增线路
          </el-button>
          <!--下拉菜单-->
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="onClickCreateLine">新建线路</el-dropdown-item>
              <el-dropdown-item @click="onClickCopyLine">复制线路</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!--表格-->
    <el-table
      v-loading="loading"
      stripe
      border
      :data="lineList"
      row-key="lineNumber"
      @selection-change="handleSelectionChange"
      :header-cell-style="tableHeaderStyle"
    >
      <el-table-column width="36" align="left" type="selection"></el-table-column>
      <el-table-column align="left" fixed="left" prop="lineName" label="线路名称" min-width="200" show-overflow-tooltip/>
      <el-table-column align="center" fixed="left" prop="lineType" label="线路类型" width="80">
        <template #default="scope:{row:{lineType:MerchantLineTypeEnum}}">
          {{ getMerchantLineTypeText(scope.row?.lineType) }}
        </template>
      </el-table-column>
      <el-table-column align="left" fixed="left" prop="lineNumber" label="线路编号" min-width="160"/>
      <el-table-column align="left" prop="concurrentLimit" label="并发限制" width="80"/>
      <el-table-column align="center" prop="updateTime" label="更新时间" width="160" show-overflow-tooltip>
        <template v-slot="scope:{row:{updateTime:string}}">
          {{ dayjs(scope.row.updateTime).format('YYYY-MM-DD HH:mm:ss') }}
        </template>
      </el-table-column>
      <el-table-column align="center" prop="enableStatus" label="启用状态" width="100">
        <template v-slot="{row}">
          <div class="status-box" :class="row.enableStatus===MerchantLineEnableStatusEnum['启用'] ? 'green-status' : 'orange-status'">
            {{ findValueInEnum(row.enableStatus, MerchantLineEnableStatusEnum) || '-' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column align="left" label="适用行业" width="100">
        <template #default="{row}:{row:MerchantLineInfo}">
          {{ getSecondIndustriesStr(row.secondIndustries) }}
        </template>
      </el-table-column>
      <el-table-column v-if="!!merchantStore.currentAccount?.accountEnable" align="right" label="操作" fixed="right" width="160">
        <template v-slot="scope:{row:{enableStatus:any}}">
          <el-button type="primary" link @click="clickShowRegion(scope.row)">
            支持地区
          </el-button>
          <el-button type="primary" link @click="clickEditLine(scope.row)">
            编辑
          </el-button>
          <el-button
            :type="scope.row.enableStatus === MerchantLineEnableStatusEnum['停用'] ? 'primary' : 'danger'"
            link
            @click="clickSwitchLineStatus(scope.row)"
          >
            {{ scope.row.enableStatus === MerchantLineEnableStatusEnum['停用'] ? '启用' : '停用' }}
          </el-button>
        </template>
      </el-table-column>
      <!--空数据提示-->
      <template #empty>
        <el-empty description="暂无数据" />
      </template>
    </el-table>

    <!--分页条-->
    <!--列表有数据时才显示-->
    <div
      v-if="lineAllList.length"
      class="tw-flex tw-flex-col tw-justify-center tw-items-end"
    >
      <PaginationBox
        class="tw-grow-0 tw-shrink-0"
        :pageSize="linePageSize"
        :pageSizeList="linePagerSizeList"
        :currentPage="linePageNum"
        :total="lineTableSize"
        @search="updatePage(linePageNum, linePageSize)"
        @update="updatePage"
      >
      </PaginationBox>
    </div>

  </div>
  <!--复制商户线路弹窗-->
  <DialogCopyLine
    :visible="dialogCopyLineVisible"
    @close="onCloseDialogCopyLine"
    @confirm="onConfirmDialogCopyLine"
  />

  <!--商户线路支持地区弹窗-->
  <DialogRegion
    :visible="dialogRegionVisible"
    :data="dialogRegionData"
    @close="onCloseDialogRegion"
  />
</template>

<script setup lang="ts">
import { tableHeaderStyle } from '@/assets/js/constant'
import { enum2Options, findValueInEnum, updateCurrentPageList } from '@/utils/utils'
import PaginationBox from '@/components/PaginationBox.vue'
import { defineAsyncComponent, nextTick, ref, reactive, watch } from 'vue'
import to from 'await-to-js'
import { ElMessage } from 'element-plus'
import { MerchantAccountInfo, MerchantLineTypeEnum, MerchantLineInfo, MerchantLineEnableStatusEnum, MerchantLineConstituteParams,
  MerchantLineInfoParams, 
 } from '@/type/merchant'
import { useMerchantStore } from '@/store/merchant'
import { useGlobalStore } from '@/store/globalInfo'
import { merchantModel } from '@/api/merchant'
import { CloseBold, Plus, Select, Search } from '@element-plus/icons-vue'
import router from '@/router'
import { getMerchantLineTypeText } from '@/utils/line'
import dayjs from 'dayjs'
import { trace } from '@/utils/trace'
import Confirm from '@/components/message-box'

// ---------------------------------------- 通用 开始 ----------------------------------------

const DialogCopyLine = defineAsyncComponent(() => import('./Detail/DialogCopyLine.vue'))
const DialogRegion = defineAsyncComponent(() => import('./DialogRegion.vue'))

const merchantStore = useMerchantStore()
const globalStore = useGlobalStore()

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 表格 开始 ----------------------------------------

// 列表，正在加载
const loading = ref<boolean>(false)
/**
 * 点击新建线路按钮
 */
 const onClickCreateLine = () => {
  // 缓存数据
  merchantStore.merchantLineList = lineAllList.value
  // 初始化商户线路
  merchantStore.editingMerchantLine = { id: -1 }
  merchantStore.readonly = false
  // 切换路由到编辑线路子页面
  router.push({ name: 'MerchantLineDetail' })
}
/**
 * 点击复制线路按钮
 */
const onClickCopyLine = () => {
  // 缓存数据
  merchantStore.merchantLineList = lineAllList.value
  // 显示弹窗
  dialogCopyLineVisible.value = true
}

// ---------------------------------------- 列表标签卡 通用 结束 ----------------------------------------

// ---------------------------------------- 线路列表 开始 ----------------------------------------

// 全部数据，接口返回
const lineAllList = ref<MerchantLineInfo[]>([])

// 当前页，页面展示
const lineList = ref<MerchantLineInfo[]>()
// 当前页码，从1开始
const linePageNum = ref<number>(1)
// 每页大小
const linePageSize = ref<number>(20)
// 每页大小的可选数值
const linePagerSizeList: number[] = [20, 50, 100]
// 列表总长度
const lineTableSize = ref<number>(0)

// 搜索条件表单
const searchForm = reactive<{
  lineName?: string
  lineNumber?: string
  enableStatus?: MerchantLineEnableStatusEnum
  secondIndustries?: string[]
}>({
  lineName: undefined,
  lineNumber: undefined,
  enableStatus: undefined,
  secondIndustries: undefined
})

// 搜索条件，适用行业，级联选择器，数据内容
const industryOptions = ref<{
  label: string, value: string, children: { label: string, value: string }[]
}[]>([])
// 搜索条件，适用行业，级联选择器，配置信息
const industryProps = { multiple: true, emitPath: false }

/**
 * 更新全部二级行业列表
 */
const updateAllIndustryList = async () => {
  await globalStore.getAllIndustryList()
  const allIndustryList = globalStore.allIndustryList
  industryOptions.value = allIndustryList?.map((primaryItem) => {
    return {
      label: primaryItem?.primaryIndustry ?? '',
      value: primaryItem?.primaryIndustry ?? '',
      children: (primaryItem?.secondaryIndustries ?? []).map((secondItem) => {
        return {
          label: secondItem?.name ?? '',
          value: secondItem?.name ?? '',
        }
      })
    }
  })
}
/**
 * 按条件搜索线路列表
 * @param {number} pageNum 指定更新后跳转的页码，参数为空默认回到第1页
 * @param {Function} callback 成功更新列表后的回调函数
 */
const updateLineAllListByCondition = async (pageNum: number = 1, callback?: Function) => {
  if (!merchantStore.currentAccount?.groupId) return
  if (loading.value) return ElMessage.warning('请务重复请求')
  loading.value = true
  try {
    // 处理参数
    const params: MerchantLineInfoParams = {
      groupId: merchantStore.currentAccount?.groupId ?? '',
      ...searchForm,
    }

    // 请求接口
    const res = <MerchantLineInfo[]>await merchantModel.getMerchantLineListByConditionForOperation(params)
    // 列表需要双重排序，先按启用状态排序，再按更新时间倒序排序
    lineAllList.value = (Array.isArray(res) ? res : []).sort((a, b) => {
      if (a.enableStatus !== b.enableStatus) {
        // 启用状态的排在前面
        return a.enableStatus === MerchantLineEnableStatusEnum['启用'] ? -1 : 1
      }
      // 更新时间新的排在前面
      return dayjs(a.updateTime).isAfter(b.updateTime) ? -1 : 1
    })

    // 更新列表总长度
    lineTableSize.value = lineAllList.value.length
    // 重置页码
    linePageNum.value = pageNum
    // 更新当前页码的列表数据
    updateLineList()

    if (callback) {
      const current = lineAllList.value.find((item: MerchantLineInfo) => {
        return item?.id === merchantStore?.editingMerchantLine?.id
      })
      if (current) {
        callback(current)
      }
    }
  } catch (e) {
  } finally {
    loading.value = false
  }
}
/**
 * 更新线路列表，当前页，页面展示
 */
const updateLineList = () => {
  lineList.value = updateCurrentPageList(lineAllList.value, linePageNum.value, linePageSize.value)
}
/**
 * 修改线路列表当前页码和页面大小
 */
const updatePage = (p: number, s: number) => {
  linePageNum.value = p
  linePageSize.value = s
  updateLineAllListByCondition(linePageNum.value)
}
/**
 * 线路列表，重置所有搜索条件
 */
const resetLineFilter = () => {
  searchForm.lineName = undefined
  searchForm.lineNumber = undefined
  searchForm.enableStatus = undefined
  searchForm.secondIndustries = undefined
}
/**
 * 点击搜索条件的清除按钮
 */
const clickLineResetFilter = () => {
  resetLineFilter()
}
/**
 * 点击搜索按钮
 */
const clickLineSearch = () => {
  // 执行搜索
  try {
    updateLineAllListByCondition()
  } catch (e) {
  }
}
/**
 * 点击某个线路的编辑按钮
 * @param item {MerchantLineInfo} 当前行数据
 */
const clickEditLine = (item: MerchantLineInfo) => {
  if (loading.value) {
    return
  }

  // 初始化商户线路信息
  merchantStore.editingMerchantLine = { id: -1, ...item }
  merchantStore.readonly = false

  updateLineAllListByCondition(linePageNum.value, (newItem: MerchantLineInfo) => {
    // 更新商户线路信息
    merchantStore.editingMerchantLine = { id: -1, ...newItem }
    // 切换路由到编辑线路子页面
    router.push({ name: 'MerchantLineDetail' })
  })
}
/**
 * 点击某个线路的切换状态按钮
 * @param item {MerchantLineInfo} 当前线路信息
 */
const clickSwitchLineStatus = async (item: MerchantLineInfo) => {
  // 新状态 将启用和停用互换
  const newText = item.enableStatus === MerchantLineEnableStatusEnum['启用'] ? '停用' : '启用'

  // 显示确认弹窗
  const [confrimErr] = await to(Confirm({
    text: `您确定要【${newText}】线路【${item.lineName}】吗?`,
    type: 'warning',
    title: `${newText}确认`,
    confirmText: '确认'
  }))
  // 取消二次确认
  if (!!confrimErr) return

  const params: MerchantLineInfoParams = {
    tenantLineNumber: item.lineNumber!,
    enableStatus: item.enableStatus === MerchantLineEnableStatusEnum['启用'] ? MerchantLineEnableStatusEnum['停用'] : MerchantLineEnableStatusEnum['启用'],
  }
  trace({
    page: `商户管理-${newText}【${item.lineName}】`,
    params
  })
  loading.value = true
  const [err] = await to(merchantModel.changeLineStatus(params))
  loading.value = false
  !err && ElMessage.success('线路已' + newText)
  // 需要保留当前页码
  await updateLineAllListByCondition(linePageNum.value)
}

/**
 * 获取二级行业列表的字符串形式（扁平化）
 * @param list 行业数组
 */
const getSecondIndustriesStr = (list?: string[]) => {
  return list?.length ? list.join(', ') : '-'
}

// ---------------------------------------- 复制商户线路弹窗 开始 ----------------------------------------

// 显示复制线路弹窗
const dialogCopyLineVisible = ref<boolean>(false)
/**
 * 复制线路弹窗的关闭事件回调
 */
const onCloseDialogCopyLine = () => {
  dialogCopyLineVisible.value = false
}
/**
 * 复制线路弹窗的确认事件回调
 * @param item 复制的新的线路信息
 */
const onConfirmDialogCopyLine = (item: MerchantLineInfo) => {
  // console.log('复制线路弹窗的确认事件回调', item)
  // 关闭复制线路弹窗
  dialogCopyLineVisible.value = false
  // 初始化商户线路信息
  merchantStore.editingMerchantLine = { id: -1, ...item }
  merchantStore.readonly = false
  // 切换路由到编辑线路子页面
  router.push({ name: 'MerchantLineDetail' })
}

// ---------------------------------------- 复制商户线路弹窗 结束 ----------------------------------------

// ---------------------------------------- 商户线路支持地区弹窗 开始 ----------------------------------------

// 显示地区分布弹窗
const dialogRegionVisible = ref(false)
// 商户线路支持地区数据
const dialogRegionData = ref<MerchantLineConstituteParams[]>([])

/**
 * 点击线路的查看支持地区按钮
 * @param row {MerchantLineInfo} 当前行数据
 */
const clickShowRegion = (row: MerchantLineInfo) => {
  // 将支持地区信息传给弹窗
  dialogRegionData.value = row?.supplyLineGroups ?? []
  // 显示地区分布弹窗
  dialogRegionVisible.value = true
}
/**
 * 地区分布弹窗的关闭事件回调
 */
const onCloseDialogRegion = () => {
  dialogRegionVisible.value = false
}

// ---------------------------------------- 商户线路支持地区弹窗 结束 ----------------------------------------

// 批量停用线路
const selectData = ref<MerchantLineInfo[] | null>(null)
const handleSelectionChange = (val: MerchantLineInfo[]) => {
  selectData.value = val || []
}
const batchStopLine = async () => {
  if(!selectData.value?.length) return ElMessage.warning('请选择需要批量操作的线路')

  // 显示确认弹窗
  const [confrimErr] = await to(Confirm({
    text: `您确定要批量停用【${selectData.value?.length || 0}】条线路吗?`,
    type: 'warning',
    title: `停用确认`,
    confirmText: '停用'
  }))
  // 取消二次确认
  if (!!confrimErr) return
  loading.value = true
  trace({
    page: `商户管理-批量停用`,
    params: selectData.value.map(item => item.lineNumber)
  })
  const errList: string[] = []
  for (let item of selectData.value) {
    const params: MerchantLineInfoParams = {
      tenantLineNumber: item.lineNumber!,
      enableStatus: MerchantLineEnableStatusEnum['停用'],
    }
    const [err] = await to(merchantModel.changeLineStatus(params))
    // @ts-ignore
    err && errList.push(item.lineNumber + (err.message || err.msg))
  }

  loading.value = false

  // 需要保留当前页码
  await updateLineAllListByCondition(linePageNum.value)
}

// ---------------------------------------- 线路列表 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

watch(() => merchantStore.currentAccount?.id, async (val: number | null | undefined) => {
  if (typeof val === 'number' && val > 0) {
    await updateAllIndustryList()
    // 重置搜索条件
    resetLineFilter()
    lineAllList.value = []
    lineList.value = []
    await updateLineAllListByCondition()
  }
}, { immediate: true })

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
</style>
