<template>
  <div>
    <!--搜索区块-->
    <div class="tw-flex tw-flex-col tw-p-[16px]">
      <!--输入框容器-->
      <div class="tw-grid tw-grid-cols-3 tw-gap-[8px]">
        <div>
          <el-input
            v-model.trim="scriptNameSearchVal"
            placeholder="话术名称"
            :icon="Search"
            clearable
          />
        </div>
        <div>
          <el-select v-model.trim="scriptStatusSearchVal" placeholder="话术状态" clearable style="width: 100%;">
            <el-option
              v-for="scriptStatusItem in enum2Options(MerchantScriptActionEnum)"
              :key="scriptStatusItem.name"
              :value="scriptStatusItem.value"
              :label="scriptStatusItem.name!"
            />
          </el-select>
        </div>
        <div class="tw-flex tw-justify-end">
          <!--清空搜索数据-->
          <el-button type="primary" link @click="clickScriptResetFilter">
            <el-icon size="--el-font-size-base">
              <SvgIcon name="reset" color="var(--el-color-primary)" />
            </el-icon>
            <span>重置</span>
          </el-button>
          <!--搜索按钮-->
          <el-button type="primary" link @click="clickScriptSearch">
            <el-icon size="--el-font-size-base" color="var(--el-color-primary)">
              <SvgIcon name="search" color="none" />
            </el-icon>
            <span>搜索</span>
          </el-button>
        </div>
      </div>

      <!--关联话术按钮-->
      <div v-if="!!merchantStore.currentAccount?.accountEnable" class="tw-flex tw-justify-end tw-mt-[6px]">
        <el-button type="primary" :icon="Plus" @click="linkDialogVisible=true">
          新增关联话术
        </el-button>
      </div>
    </div>

    <!--表格-->
    <el-table
      v-loading="loading"
      stripe
      :data="scriptList"
      :header-cell-style="tableHeaderStyle"
    >
      <el-table-column align="left" prop="scriptId" label="话术ID" min-width="120" show-overflow-tooltip />
      <el-table-column
        align="left"
        prop="scriptName"
        label="话术名称"
        min-width="200"
        show-overflow-tooltip
      />
      <el-table-column align="center" resizable prop="active" label="话术状态" width="120">
        <template v-slot="scope:{row:{active:any}}">
          <div
            class="status-box"
            :class="scope.row.active === MerchantScriptActionEnum['生效中'] ? 'green-status' : 'orange-status'"
          >
            {{ findValueInEnum(scope.row.active, MerchantScriptActionEnum) || '-' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="!!merchantStore.currentAccount?.accountEnable" align="right" label="操作" width="120">
        <template v-slot="scope:{row:MerchantScriptInfo}">
          <el-button type="primary" link @click="clickUnbindScript(scope.row)">
            解除关联
          </el-button>
        </template>
      </el-table-column>
      <!--空数据提示-->
      <template #empty>
        <el-empty v-if="!scriptList || scriptList.length < 1" description="暂无数据" />
      </template>
    </el-table>

    <!--分页条-->
    <!--列表有数据时才显示-->
    <div
      v-if="scriptList.length"
      class="tw-flex tw-flex-col tw-justify-center tw-items-end"
    >
      <PaginationBox
        class="tw-grow-0 tw-shrink-0"
        :pageSize="scriptPageSize"
        :pageSizeList="scriptPagerSizeList"
        :currentPage="scriptPageNum"
        :total="scriptTableSize"
        @search="updateScriptAllList"
        @update="updateScriptPage"
      >
      </PaginationBox>
    </div>
  </div>
  <!--关联话术弹窗-->
  <el-dialog
    v-model="linkDialogVisible"
    width="666px"
    align-center
    class="merchant-dialog"
    :close-on-click-modal="false"
    @close="linkDialogVisible=false"
  >
    <template #header>
      <div class="form-dialog-header">
        新增关联话术
      </div>
    </template>

    <!--弹窗主体-->
    <el-scrollbar class="form-dialog-main" view-class="form-dialog-main-inner">
      <el-form ref="addFormRef" label-width="66px">
        <el-form-item label="关联话术：">
          <el-select
            v-model.trim="scriptBindId"
            clearable
            filterable
            placeholder="请选择话术"
            fit-input-width
            :loading="loadingScriptAvailableList"
            @visible-change="handleScriptSelectorVisibleChange"
          >
            <el-option
              v-for="scriptAvailableItem in scriptAvailableList"
              :key="scriptAvailableItem.id"
              :label="scriptAvailableItem.scriptName??''"
              :value="scriptAvailableItem.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </el-scrollbar>

    <template #footer>
      <div class="form-dialog-footer">
        <el-button :icon="CloseBold" @click="linkDialogVisible=false">
          取消
        </el-button>
        <el-button :loading="loading" type="primary" :icon="Select" @click="clickBindScript">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { tableHeaderStyle } from '@/assets/js/constant'
import { enum2Options, findValueInEnum, Throttle, updateCurrentPageList } from '@/utils/utils'
import PaginationBox from '@/components/PaginationBox.vue'
import { computed, nextTick, ref, watch } from 'vue'
import to from 'await-to-js'
import { ElMessage } from 'element-plus'
import { MerchantScriptActionEnum, MerchantScriptAvailableInfo, MerchantScriptInfoParams, MerchantScriptInfo, MerchantInfo } from '@/type/merchant'
import { useMerchantStore } from '@/store/merchant'
import { CloseBold, Plus, Select, Search } from '@element-plus/icons-vue'
import { merchantModel } from '@/api/merchant'
import { trace } from '@/utils/trace'
import Confirm from '@/components/message-box'

const merchantStore = useMerchantStore()

const loading = ref<boolean>(false) // 列表，正在加载

// ---------------------------------------- 话术列表 开始 ----------------------------------------

// 全部数据，接口返回
const scriptAllList = ref<MerchantScriptInfo[]>()
// 搜索数据，在全部数据的基础上执行搜索后的结果，是全部数据的子集
const scriptSearchList = ref<MerchantScriptInfo[]>([])
// 当前页，页面展示，在搜索数据的基础上进行分页，是搜索数据的子集，是全部数据的子集
const scriptList = ref<MerchantScriptInfo[]>([])
// 当前页码，从1开始
const scriptPageNum = ref<number>(1)
// 每页大小
const scriptPageSize = ref<number>(20)
// 每页大小的可选数值
const scriptPagerSizeList: number[] = [20, 50, 100]
// 列表总长度
const scriptTableSize = ref<number>(0)

// 搜索条件，话术名称
const scriptNameSearchVal = ref<string>('')
// 搜索条件，线路状态
const scriptStatusSearchVal = ref<string>('')

// 正在加载可关联话术列表
const loadingScriptAvailableList = ref<boolean>(false)

// 可以关联的话术列表
const scriptAvailableList = ref<MerchantScriptAvailableInfo[]>([])
// 已选择的准备关联的话术ID
const scriptBindId = ref<string>('')

/**
 * 更新话术列表，全部，接口数据
 * @param pageNum 指定更新后跳转的页码，参数为空默认回到第1页
 */
const updateScriptAllList = async (pageNum: number = 1) => {
  if (!merchantStore.currentMerchant.id || !merchantStore.currentAccount.groupId) {
    scriptAllList.value = []
    return
  }
  loading.value = true

  try {
    // 处理参数
    const params: MerchantScriptInfoParams = {
      id: merchantStore.currentMerchant.id ?? -1,
      groupId: merchantStore.currentAccount.groupId ?? ''
    }

    // 请求接口
    const data = <MerchantInfo>await merchantModel.getScriptList(params)

    // 更新列表
    scriptAllList.value = data.relatedScriptList

    // 更新搜索结果
    // 忽略节流锁，因为此时已经上锁了
    updateScriptSearchList(true)

    // 重置页码
    scriptPageNum.value = pageNum
    // 更新当前页码的列表数据
    updateScriptList()
  } catch (e) {
  } finally {
    loading.value = false
  }
}
/**
 * 更新话术列表，搜索结果
 * ignoreThrottle {boolean} 忽略节流锁
 */
const updateScriptSearchList = (ignoreThrottle: boolean = false) => {
  // 节流锁上锁
  loading.value = true

  // 如果全部数据列表不存在或者为空
  if (!scriptAllList.value || scriptAllList.value.length < 1) {
    scriptSearchList.value = []
    // 节流锁解锁
    loading.value = false
    return
  }

  // 如果有全部数据，遍历列表，执行搜索

  // 对全部数据按名称匹配
  // 搜索框非空才筛选，空则返回全部
  const nameHitList = !scriptNameSearchVal.value
    ? scriptAllList.value
    : scriptAllList.value!.filter((item) => {
      return scriptNameSearchVal.value.includes(item.scriptName ?? '')
    })

  // 对命中的话术名称列表，再执行状态匹配
  // 搜索框为空或者设置的搜索全部状态时，不做筛选
  if (!scriptStatusSearchVal.value) {
    scriptSearchList.value = nameHitList
  } else {
    scriptSearchList.value = nameHitList!.filter((item) => {
      return scriptStatusSearchVal.value === item.active
    })
  }

  // 更新列表长度
  scriptTableSize.value = scriptSearchList.value?.length ?? 0

  // 节流锁解锁
  loading.value = false
}
/**
 * 更新话术列表，当前页，页面展示
 */
const updateScriptList = () => {
  scriptList.value = updateCurrentPageList(scriptSearchList.value, scriptPageNum.value, scriptPageSize.value)
}
/**
 * 修改话术列表当前页码\大小
 */
const updateScriptPage = (p: number, s: number) => {
  scriptPageNum.value = p
  scriptPageSize.value = s
  updateScriptAllList(p)
}
/**
 * 话术列表，重置所有搜索条件
 */
const resetScriptFilter = () => {
  scriptNameSearchVal.value = ''
  scriptStatusSearchVal.value = ''
}
/**
 * 点击搜索条件的清除按钮
 */
const clickScriptResetFilter = () => {
  resetScriptFilter()
}
/**
 * 点击搜索按钮
 */
const clickScriptSearch = () => {
  // 在全部话术列表里搜索匹配项
  updateScriptSearchList()
  // 更新当前页码的列表数据
  updateScriptList()
}

// ---------------------------------------- 话术列表 结束 ----------------------------------------

// ---------------------------------------- 关联话术弹窗 开始 ----------------------------------------

// 是否显示关联话术弹窗
const linkDialogVisible = ref(false)

/**
 * 获取可关联的话术列表
 */
const updateScriptAvailableList = async () => {

  loadingScriptAvailableList.value = true

  try {
    // 请求接口
    const res = await merchantModel.getScriptAvailableList({
      status: MerchantScriptActionEnum['生效中']
    })
    // 过滤掉已关联的话术
    scriptAvailableList.value = (Array.isArray(res) ? res : []).filter((item) => {
      return (scriptAllList.value ?? []).findIndex(s => s.scriptStringId === item.scriptStringId) === -1
    })
  } catch (e) {
  } finally {
    // 节流锁解锁
    loadingScriptAvailableList.value = false
  }
}
/**
 * 关联话术下拉选择框显示隐藏
 */
const handleScriptSelectorVisibleChange = (visible: boolean) => {
  // 显示下拉框
  if (visible) {
    // 获取可关联话术列表
    updateScriptAvailableList()
  }
}
/**
 * 点击关联话术按钮
 */
const clickBindScript = async () => {
  // 如果待关联的话术ID为空，提示用户
  if (!scriptBindId.value) {
    ElMessage({
      message: '请选择需要关联的话术',
      duration: 3000,
      type: 'warning',
    })
    return
  }

  const selectedId = parseInt(scriptBindId.value)

  // 如果需要关联的话术ID在已关联的话术列表里能找到，则判定为重复关联
  const findIndex = scriptAllList.value?.findIndex((item) => {
    return item.scriptId === selectedId
  }) ?? -1
  if (findIndex > -1) {
    ElMessage({
      message: '此话术已关联，无需重复关联',
      duration: 3000,
      type: 'warning',
    })
    return
  }

  // 根据ID查找到这个话术的名称，用于确认弹窗的显示内容
  const current = scriptAvailableList.value.find((item: MerchantScriptAvailableInfo) => {
    return item.id === selectedId
  })
  // 根据ID查找到这个话术的StringId
  const scriptStringId = current?.scriptStringId ?? ''

  try {
    // 处理参数
    const params = {
      scriptId: selectedId,
      tenantId:  merchantStore.currentMerchant.id ?? -1,
      groupId: merchantStore.currentAccount.groupId ?? '',
      scriptStringId: scriptStringId,
    }

    // 埋点
    trace({
      page: '商户管理-绑定关联话术',
      params: params
    })

    // 请求接口
    await merchantModel.bindMerchantScript(params)

    ElMessage({
      message: '话术关联成功',
      duration: 3000,
      type: 'success',
    })

    // 清空选中的话术
    scriptBindId.value = ''
  } catch (e) {
    ElMessage({
      message: '话术关联失败',
      duration: 3000,
      type: 'error',
    })
  } finally {
    // 关闭弹窗
    linkDialogVisible.value = false
    // 更新话术列表
    await updateScriptAllList()
  }
}
/**
 * 点击解除关联话术按钮
 * @param item 当前话术ID
 */
const clickUnbindScript = (item: MerchantScriptInfo) => {
  // 显示确认弹窗
  Confirm({
    text: `您确定要以解除话术【${item.scriptName}】的关联吗?`,
    type: 'warning',
    title: `解除关联话术`,
    confirmText: '确认'
  }).then(async () => {
    try {
      // 处理参数
      let selectedId = parseInt(item.scriptId + '')
      if (isNaN(selectedId)) {
        selectedId = -1
      }

      const params = {
        scriptId: selectedId,
        scriptStringId: item.scriptStringId || '',
        tenantId:  merchantStore.currentMerchant.id ?? -1,
        groupId: merchantStore.currentAccount.groupId ?? '',
        id: merchantStore.currentAccount.id ?? -1,
      }

      // 埋点
      trace({
        page: '商户管理-解除关联话术',
        params: params
      })

      // 请求接口
      await merchantModel.unbindMerchantScript(params)

      ElMessage({
        message: '话术已解除关联',
        duration: 3000,
        type: 'success',
      })
    } catch (e) {
      ElMessage({
        message: '话术无法解除关联',
        duration: 3000,
        type: 'error',
      })
    } finally {
      // 更新列表
      // 需要保留当前页码
      await updateScriptAllList(scriptPageNum.value)
      // 更新可关联话术列表
      await updateScriptAvailableList()
    }
  }).catch(() => {
  })
}

// ---------------------------------------- 关联话术弹窗 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

watch(() => merchantStore.currentAccount?.id, async (val: number | null | undefined) => {
  if (typeof val === 'number' && val > 0) {
    scriptAllList.value = []
    scriptList.value = []
    scriptBindId.value = ''
    resetScriptFilter()
    await updateScriptAllList()
  }
}, { immediate: true })

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
</style>
