<template>
  <el-drawer
    v-model="phoneDrawerVisible"
    size="90%"
    title="坐席工作台"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :with-header="false"
    :modal="true"
    :z-index="15"
    :destroy-on-close="true"
  >
    <!--模块主体 内容器-->
    <div class="module-container-inner">
      <!--抽屉顶部-->
      <div class="drawer-header">
        <div class="drawer-header-title">坐席工作台</div>
        <div class="drawer-header-time reception-time">
          <el-icon :size="16" color="#fff" class="tw-mr-[4px]">
            <SvgIcon name="seat" />
          </el-icon>
          <span>接待时长：</span><span>{{ formatDuration(seatPhoneStore.receptionSecond) }}</span>
        </div>
        <!--客户信息-->
        <div class="drawer-header-info">
          <!--手机号-->
          <div v-show="seatPhoneStore.phone" class="info-phone">
            <!--手机号文本-->
            <span class="tw-mr-[8px]">{{ seatPhoneStore.phone }}</span>
            <!--复制按钮-->
            <el-button link class="tw-mr-[6px]" type="primary" @click="copyText(seatPhoneStore.phone??'')">
              复制
            </el-button>
            <!--查看明文按钮-->
            <el-button
              v-if="!!seatPhoneStore.phone && !!permissionsForPlaintext && callType!==SeatCallTypeEnum.DIRECT"
              link
              type="primary"
              class="tw-mr-[6px]"
              @click="showPlaintextAction(seatPhoneStore.phone)"
            >
              查看明文
            </el-button>
          </div>
          <!--姓名-->
          <div v-show="seatPhoneStore.callCarryInfo.name" class="info-name">
            <span>姓名：</span>
            <span>
              {{ seatPhoneStore.callCarryInfo.name ?? '' }}
            </span>
          </div>
          <!--年龄-->
          <div v-show="typeof seatPhoneStore.callCarryInfo.age === 'number'" class="info-name">
            <span>年龄：</span>
            <span :class="getAgeClassName(seatPhoneStore.callCarryInfo.age)">
              {{ seatPhoneStore.callCarryInfo.age ?? '' }}
            </span>
          </div>
          <!-- 测试环境快进按钮 -->
          <el-button
            v-show="isTestEnv && seatPhoneStore.receptionSecond > 0"
            type="warning"
            size="small"
            class="tw-ml-[8px]"
            @click="handleFastForward"
          >
            快进到{{ RECEPTION_WARNING_THRESHOLD_MINUTES - 1 }}:50
          </el-button>
          <!-- 运营商和归属地 -->
          <span class="tw-flex tw-items-center tw-mx-[8px] tw-text-[14px]">
            <img class="tw-w-[24px] tw-h-[24px]" :src="operatorImgSrc" :alt="clientOperator" />
            <span class="tw-ml-[8px] tw-font-[600]">{{ clientLocation }}</span>
          </span>
        </div>

        <div class="drawer-header-button">
          <!--麦克风静音按钮-->
          <el-button v-show="muteVisible" link @click="onClickMute">
            <el-icon v-if="muted" :size="24" color="#165DFF">
              <SvgIcon name="microphone-muted" />
            </el-icon>
            <el-icon v-else :size="24" color="#165DFF">
              <SvgIcon name="microphone" />
            </el-icon>
          </el-button>

          <!--挂断电话按钮-->
          <el-button
            v-show="hangupVisible"
            link
            :disabled="hangupDisabled"
            class="header-button"
            @click="onClickHangup"
          >
            <el-icon :size="24" color="#E54B17">
              <SvgIcon name="hangup" />
            </el-icon>
            <span>{{ hangupText }}</span>
          </el-button>

          <!--退出监听按钮-->
          <el-button v-show="exitMonitorVisible" class="header-button tw-text-[#666]" @click="onClickExitMonitor">
            <el-icon :size="16" color="#666">
              <CloseBold />
            </el-icon>
            <span>退出监听</span>
          </el-button>
        </div>
      </div>

      <!--抽屉主体-->
      <div class="workbench-box" :class="{'hide-left-section':!leftSectionVisible}">
        <!--左侧区域-->
        <template v-if="leftSectionVisible">
          <div class="left-top-section">
            <div class="section-header">
              表单收集
            </div>
            <el-scrollbar wrap-class="section-main">
              <FormRecord
                ref="formRecordRef"
                :formSetting="formSetting"
                :formRecordContent="formContent"
                :requiredField="false"
                @update:formRecordContent="onUpdateFormRecordContent"
              />
            </el-scrollbar>
          </div>

          <div class="left-bottom-section">
            <div class="section-header">
              客户跟进
            </div>
            <el-scrollbar wrap-class="section-main">
              <el-form :model="clientForm" label-position="right" label-width="100px">
                <el-form-item label="星标：">
                  <el-button link @click="onClickStar">
                    <el-icon :size="20" color="#FFAA3D">
                      <SvgIcon v-show="clientForm.star" name="star-active" color="inherit" />
                      <SvgIcon v-show="!clientForm.star" name="star-inactive" color="inherit" />
                    </el-icon>
                  </el-button>
                </el-form-item>

                <el-form-item label="跟进状态：" required>
                  <el-select
                    v-model="clientForm.followUpStatus"
                    style="width: 140px;"
                    @visible-change="onFollowUpStatusVisibleChange"
                  >
                    <!--表单收集的表单校验通过时才显示跟进成功-->
                    <el-option
                      v-for="followUpStatusItem in followUpStatusList"
                      :index="followUpStatusItem.value"
                      :label="followUpStatusItem.name"
                      :value="followUpStatusItem.value"
                    />
                  </el-select>
                  <!--信息提示图标-->
                  <div class="tw-flex tw-justify-center tw-items-center tw-ml-[8px]">
                    <el-tooltip
                      content="必须填写表单收集中的所有必填项，才能将状态更改为“跟进成功”。"
                      placement="top"
                      effect="dark"
                    >
                      <el-icon size="20">
                        <WarningFilled />
                      </el-icon>
                    </el-tooltip>
                  </div>
                </el-form-item>

                <el-form-item label="下次跟进时间：">
                  <a-date-picker
                    v-model:value="clientForm.nextFollowUpTime"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    show-time
                    placeholder="选择下次跟进时间"
                    allow-clear
                    :locale="antdLocaleConfig"
                    :presets="nextFollowUpTimePresets"
                    :disabled-date="disabledDate"
                    @change="onChangeNextFollowUpTime"
                  />
                </el-form-item>

                <el-form-item label="跟进备注：">
                  <el-input
                    v-model="clientForm.note"
                    placeholder="填写备注信息"
                    clearable
                    type="textarea"
                    resize="none"
                    show-word-limit
                    maxlength="250"
                  />
                </el-form-item>
              </el-form>
            </el-scrollbar>
          </div>
        </template>

        <!--中间区域-->
        <div class="center-section">
          <!--通话记录-->
          <div class="workbench-call-record">
            <template v-if="callRecordList.length">
              <CallRecordDialogBox
                :dataList="callRecordList"
                :startEndInfo="startEndInfo"
                :clearAudio="true"
                :keepLatest="true"
                :paddingBottom="true"
              />
            </template>
            <template v-else>
              <el-empty>
                {{ callInfo.callId || '-' }}
                <br>
                {{
                  seatPhoneStore.callRecordForManualDirect.recordId
                  ?? seatPhoneStore.callRecordForHumanMachine.recordId
                  ?? '-'
                }}
                <br>
                {{ callType || '-' }}
                <br>
                {{ seatStatus || '-' }}
              </el-empty>
            </template>
          </div>

          <!--按钮容器-->
          <div class="workbench-button-box">
            <div
              v-show="monitorVisible"
              class="workbench-button button-green hover-light"
              @click="onClickMonitor"
            >
              {{ '监听 (' + leftTransferSecond + 's)' }}
            </div>

            <div
              v-show="acceptVisible"
              class="workbench-button button-green hover-light"
              @click="onClickAccept"
            >
              {{ '接听 (' + leftTransferSecond + 's)' }}
            </div>

            <div
              v-show="interveneVisible"
              class="workbench-button button-green hover-light"
              :class="{'disabled':interveneDisabled}"
              @click="onClickIntervene"
            >
              <el-icon v-show="seatPhoneStore.loadingIntervene" :size="18" class="loading-icon">
                <Loading />
              </el-icon>
              介入
            </div>

            <div
              v-show="submitAndNextVisible"
              class="workbench-button button-green hover-light"
              @click="onClickSubmit(true)"
            >
              <el-icon v-show="loadingSubmit" :size="18" class="loading-icon">
                <Loading />
              </el-icon>
              {{ '提交并拨打下一个 (' + processSecond + 's)' }}
            </div>

            <div
              v-show="submitVisible"
              class="workbench-button"
              :class="callType===SeatCallTypeEnum.DIRECT?'button-white hover-dark':'button-green hover-light'"
              @click="onClickSubmit(false)"
            >
              <el-icon v-show="loadingSubmit" :size="18" class="loading-icon">
                <Loading />
              </el-icon>
              {{ '提交 (' + processSecond + 's)' }}
            </div>

            <div
              v-show="sendSmsVisible"
              class="workbench-button button-white hover-dark"
              :class="getSendSmsButtonClassName"
              @click="onClickSendSms"
            >
              发送短信
            </div>
          </div>
        </div>

        <!--右侧区域-->
        <div class="right-section">
          <!--人工直呼-->
          <template v-if="callType===SeatCallTypeEnum.DIRECT">
            <RightSectionManualCall />
          </template>

          <!--人机协同-->
          <template v-else>
            <RightSectionHumanMachine :aiRecord="aiRecord" @update:humanRecord="updateHumanRecord" />
          </template>
        </div>
      </div>
    </div>
  </el-drawer>

  <!--退出监听弹窗-->
  <el-dialog
    v-model="dialogExitMonitorVisible"
    width="480px"
    class="seat-workbench-dialog exit-monitor-dialog"
    align-center
    :show-close="exitMonitorCancelButtonVisible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="closeDialogExitMonitor"
  >
    <template #header>
      <div class="form-dialog-header">
        未介入原因
      </div>
    </template>

    <div class="form-dialog-main">
      <el-input
        v-model="exitMonitorReason"
        type="textarea"
        maxlength="250"
        show-word-limit
        resize="none"
        rows="6"
        placeholder="填写未介入原因（必填）"
      />
      <div class="tw-font-bold tw-mt-[8px]">快捷选项：</div>
      <div class="tw-flex tw-items-center tw-mt-[8px]">
        <el-button link type="primary" @click="onClickExitMonitorShortcut('小助理')">小助理</el-button>
        <el-button link type="primary" @click="onClickExitMonitorShortcut('无介入必要')">无介入必要</el-button>
        <!--<el-button link type="primary" @click="onClickExitMonitorShortcut('介入后无声音')">介入后无声音</el-button>-->
        <el-button link type="primary" @click="onClickExitMonitorShortcut('介入不了')">介入不了</el-button>
        <el-button link type="primary" @click="onClickExitMonitorShortcut('被叫挂断')">被叫挂断</el-button>
        <el-button link type="primary" @click="onClickExitMonitorShortcut('AI成功')">AI成功</el-button>
      </div>
    </div>

    <template #footer>
      <div class="form-dialog-footer">
        <el-button v-if="exitMonitorCancelButtonVisible" :icon="CloseBold" @click="onClickCancelExitMonitor">
          取消
        </el-button>
        <el-button type="primary" :icon="Select" :loading="loadingExitMonitor" @click="onClickConfirmExitMonitor">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!--挂断电话弹窗-->
  <el-dialog
    v-model="dialogHangupVisible"
    width="480px"
    class="seat-workbench-dialog hangup-dialog"
    align-center
    :close-on-click-modal="false"
    @close="closeDialogHangup"
  >
    <template #header>
      <div class="form-dialog-header">
        挂断电话
      </div>
    </template>

    <div class="form-dialog-main">
      <div class="form-dialog-content">
        <el-icon :size="20" color="#FFAA3D">
          <SvgIcon name="fail" />
        </el-icon>
        <span class="tw-ml-[4px]">确定要挂断电话吗？</span>
      </div>
    </div>

    <template #footer>
      <div class="form-dialog-footer">
        <el-button :icon="CloseBold" @click="onClickCancelDialogHangup">
          取消
        </el-button>
        <el-button type="primary" :icon="Select" @click="onClickConfirmDialogHangup">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!--发送短信弹窗-->
  <SendSmsDialog v-if="seatPage === SeatPageEnum.PHONE" />

  <!-- 查看明文弹窗 -->
  <PlaintextDialog v-model:data="showPlaintextPhone" />
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import { computed, defineAsyncComponent, nextTick, onBeforeUnmount, onMounted, reactive, ref, watch } from 'vue'
import { HangupEnum, RecordDialogueData, TaskCallRecordItem } from '@/type/task'
import { CloseBold, Loading, Select, WarningFilled } from '@element-plus/icons-vue'
import { seatCallRecordModel, seatFormRecordModel, seatWorkbenchCallModel } from '@/api/seat'
import {
  CallCarryInfo,
  SeatCallParam,
  SeatCallResponse,
  SeatCallTypeEnum,
  SeatLogActionEnum,
  SeatLogTypeEnum,
  SeatPageEnum,
  SeatStatusEnum
} from '@/type/seat'
import { ClueItem, CollectionFormItem, FollowUpStatusEnum, FollowUpTypeEnum, FormRecordItem } from '@/type/clue'
import { copyText, findValueInEnum, formatDuration, Throttle } from '@/utils/utils'
import { useSeatPhoneStore } from '@/store/seat-phone'
import { storeToRefs } from 'pinia'
import dayjs, { Dayjs } from 'dayjs'
import { antdLocaleConfig } from '@/assets/js/constant'
import to from 'await-to-js'
import { useUserStore } from '@/store/user'
import { useSmsStore } from '@/store/sms'
import routeMap from '@/router/asyncRoute/route-map'
import { trace } from '@/utils/trace'
import { useCallSettingStore } from '@/store/seat/call-setting'
import { useSeatInfoStore } from '@/store/seat/seat-info'
import { useInteractionStore } from '@/store/seat/interaction'
import { useSeatSettingStore } from '@/store/seat/seat-setting'
import yd from '@/assets/img/移动.png'
import lt from '@/assets/img/联通.png'
import dx from '@/assets/img/电信.png'
import xn from '@/assets/img/虚拟.png'
import wz from '@/assets/img/未知.png'
import qb from '@/assets/img/全部.png'

// 动态引入组件
const FormRecord = defineAsyncComponent(() => import('@/components/clue/FormRecord.vue'))
const CallRecordDialogBox = defineAsyncComponent(() => import('@/components/record/CallRecordDialogBox.vue'))
const RightSectionHumanMachine = defineAsyncComponent(() => import('./RightSection/HumanMachine.vue'))
const RightSectionManualCall = defineAsyncComponent(() => import('./RightSection/ManualCall.vue'))
const SendSmsDialog = defineAsyncComponent(() => import('@/components/sms/SendSmsDialog.vue'))
const PlaintextDialog = defineAsyncComponent({ loader: () => import('@/components/record/PlaintextDialog.vue') })

// ---------------------------------------- 通用 开始 ----------------------------------------

const userStore = useUserStore()
const seatPhoneStore = useSeatPhoneStore()
const {
  currentClue,
  callType,
  callInfo,
  dialogExitMonitorVisible,
} = storeToRefs(seatPhoneStore)
const smsStore = useSmsStore()
const callSettingStore = useCallSettingStore()
const { leftTransferSecond, totalPostSecond } = storeToRefs(callSettingStore)
const seatInfoStore = useSeatInfoStore()
const {
  seatStatus,
  seatTaskList,
  seatPage,
} = storeToRefs(seatInfoStore)
const interactionStore = useInteractionStore()
const { delayInterveneEnabled } = storeToRefs(interactionStore)
const seatSettingStore = useSeatSettingStore()
const { seatSetting } = storeToRefs(seatSettingStore)

// 接待时长警告阈值（分钟）
const RECEPTION_WARNING_THRESHOLD_MINUTES = 25

// 判断是否为测试环境
const isTestEnv = computed(() => {
  return location.protocol === 'http:'
})

// 查看明文权限
const permissionsForPlaintext = computed(() => userStore.permissions[routeMap['坐席工作台'].id]?.includes(routeMap['坐席工作台'].permissions['查看明文']))

/**
 * 快进到警告阈值前10秒（测试环境专用）
 */
const handleFastForward = () => {
  seatPhoneStore.fastForwardReceptionTimer()
  ElMessage({
    message: `已快进到${RECEPTION_WARNING_THRESHOLD_MINUTES - 1}分50秒`,
    type: 'success',
    duration: 2000
  })
}

// 是否显示通话抽屉
const phoneDrawerVisible = ref<boolean>(false)
// 当页面模式切换到通话时，显示抽屉
watch(seatPage, (val: SeatPageEnum) => {
  phoneDrawerVisible.value = val === SeatPageEnum.PHONE
  // 隐藏抽屉时，清空通话记录，以便下次使用时不会有上次通话的残留
  if (val !== SeatPageEnum.PHONE) {
    callRecordList.value = []
    startEndInfo.value = []
  }
})

// 是否显示左侧区块
const leftSectionVisible = computed(() => {
  // 人工直呼 通话
  // 人工直呼 话后处理
  // 人机协同 介入
  // 人机协同 话后处理
  // return seatStatus.value === SeatStatusEnum.MANUAL_DIRECT_DIALING
  //   || seatStatus.value === SeatStatusEnum.MANUAL_DIRECT_POSTING
  //   || seatStatus.value === SeatStatusEnum.HUMAN_MACHINE_DIALING
  //   || seatStatus.value === SeatStatusEnum.HUMAN_MACHINE_POSTING

  // 条件太多太麻烦了，取反
  // 人机协同弹窗、监听时，不显示左侧表单收集和跟进记录，其余通话状态都显示
  return seatStatus.value !== SeatStatusEnum.HUMAN_MACHINE_WINDOW
    && seatStatus.value !== SeatStatusEnum.HUMAN_MACHINE_LISTEN
})

// 麦克风是否静音
const muted = ref<boolean>(false)

/**
 * 清空表单
 */
const resetForm = () => {
  // 表单收集
  formSetting.value = []
  formContent.value = []
  // 客户跟进
  Object.assign(clientForm, new ClientFollow())
  // AI记录
  aiRecord.value = {}
  // 人工记录
  humanRecord.value = {}
}

const emits = defineEmits([
  'update'
])

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 顶部 开始 ----------------------------------------

/**
 * 获取年龄文本样式
 * @param {number|null} age 年龄数值
 */
const getAgeClassName = (age?: number | null) => {
  if (Number(age) <= 17 || 80 <= Number(age)) {
    return {
      'tw-text-[var(--primary-red-color)]': true
    }
  }
  return ''
}

// 是否显示查看明文按钮，当存在recordId且为人机协同时支持查看明文
const showPlaintextPhone = ref<string>('')
/**
 * 查看明文函数
 */
// 查看明文，仅支持人机协同号码、人工标签为A时查看
const showPlaintextAction = (val: string) => {
  if (!humanRecord.value?.intentionClass?.includes('A')) {
    ElMessage.warning({
      message: '不满足查看条件',
      duration: 2000
    })
    return
  }
  showPlaintextPhone.value = val
}

// 全部运营商的图png
const imgMap = {
  '移动': yd,
  '联通': lt,
  '电信': dx,
  '全部': qb,
  '虚拟': xn,
  '未知': wz,
}
// 客户运营商名称
const clientOperator = computed(() => {
  return aiRecord.value?.operator || currentClue.value?.operator || '未知'
})
// 客户运营商图片
const operatorImgSrc = computed(() => {
  return imgMap[clientOperator.value]
})
// 客户归属地（省市）
const clientLocation = computed(() => {
  if (aiRecord.value?.province || aiRecord.value?.city) {
    return `${aiRecord.value?.province || ''}-${aiRecord.value?.city || ''}`
  }
  else if (currentClue.value?.province || currentClue.value?.city) {
    return `${currentClue.value?.province || ''}-${currentClue.value?.city || ''}`
  } else {
    return ''
  }
})

// ---------------------------------------- 顶部 结束 ----------------------------------------

// ---------------------------------------- 表单收集 开始 ----------------------------------------

// 表单内容
interface FormRecordItemContent {
  id?: number,
  formCollectionId: number,
  content: string
}

// 表单项参数
interface FormContentItemUpdateParam {
  // 属性值，比如name, age
  prop: string
  // 展示文本，比如姓名，年龄
  text: string
  // 新值
  value: any
}

// 表单收集 组件DOM
const formRecordRef = ref()
// 表单收集 表单设置
const formSetting = ref<CollectionFormItem[]>([])
// 表单收集 表单内容
const formContent = ref<FormRecordItemContent[]>([])

/**
 * 从线索里查找并更新表单收集的表单项值
 * @param {FormContentItemUpdateParam} params 表单项参数
 */
const updateFormContentItem = (params: FormContentItemUpdateParam) => {
  if (params.prop && params.text) {
    // 在表单设置（表单结构）里找到表单项ID
    const nameFormItem = formSetting.value.find((formSettingItem: CollectionFormItem) => {
      return formSettingItem.collectionItemName === params.text
    })
    // console.log('nameFormItem?.id', nameFormItem?.id)
    // 在表单内容里根据表单项ID找到对应表单项
    // console.log('formContent.value', JSON.parse(JSON.stringify(formContent.value)))
    if (typeof nameFormItem?.id === 'number') {
      const formContentIndex = formContent.value.findIndex((formContentItem: FormRecordItemContent) => {
        return formContentItem.formCollectionId === nameFormItem?.id
      })
      // console.log('formContentIndex', formContentIndex)
      // 修改表单项的值
      if (formContentIndex > -1) {
        // 可以找到已有表单项
        formContent.value[formContentIndex].content = params.value + ''
      } else {
        // 没有找到已有表单项
        formContent.value.push({
          formCollectionId: nameFormItem?.id,
          content: params.value
        })
      }
      // const hintText = '表单收集项【' + params.text + '】自动填写为【' + params.value + '】'
      // console.log(hintText)
      // ElMessage.success(hintText)
    }
  }
}
/**
 * 获取最新表单设置
 */
const updateFormSetting = async () => {
  formSetting.value = <CollectionFormItem[]>await seatFormRecordModel.getEnableFormSettingInfo() || []
  // console.log('获取最新表单设置', JSON.parse(JSON.stringify(formSetting.value)))
}
/**
 * 获取最新表单内容
 */
const updateFormRecordContent = async () => {
  const clueId = currentClue.value.id ?? null
  if (clueId !== null) {
    const res = <FormRecordItem>await seatFormRecordModel.getFormRecordByClueId({
      clueId,
    })
    formContent.value = res?.fromCollectionContentList ?? []
    // console.log('获取最新表单内容', JSON.parse(JSON.stringify(formContent.value)))
  }
}
// 监听坐席状态，当变成通话时，更新表单收集内容
watch(seatStatus, async (val: SeatStatusEnum) => {
  // 人工直呼 通话
  // 人机协同 介入
  if (val === SeatStatusEnum.MANUAL_DIRECT_DIALING || val === SeatStatusEnum.HUMAN_MACHINE_DIALING) {
    // 更新表单收集的数据结构
    await updateFormSetting()
    // 更新表单收集的记录内容
    await updateFormRecordContent()
    // 表单收集进行表单校验，并且更新跟进状态可选列表
    validFormRecord()
    // 自动填充表单收集里的姓名和年龄
    updateFormClientNameAndAge(seatPhoneStore.callCarryInfo)
  }
})
/**
 * 自动填充表单收集里的姓名和年龄
 * @param val 通话携带信息
 */
const updateFormClientNameAndAge = (val: CallCarryInfo) => {
  // console.log('updateFormClientNameAndAge', JSON.parse(JSON.stringify(val)))
  if (val?.name) {
    // console.log('updateFormClientNameAndAge name', val?.name)
    updateFormContentItem({
      prop: 'name',
      text: '姓名',
      value: val.name
    })
  }
  if (typeof val?.age === 'number') {
    // console.log('updateFormClientNameAndAge age', val?.age)
    updateFormContentItem({
      prop: 'age',
      text: '年龄',
      value: val.age
    })
  }
}
// 如果显示通话抽屉时，通话携带信息里有姓名和年龄，就把信息填进表单收集里
watch(() => seatPhoneStore.callCarryInfo, (val: CallCarryInfo) => {
  // console.log('watch seatPhoneStore.callCarryInfo', JSON.parse(JSON.stringify(val)))
  if (seatPage.value === SeatPageEnum.PHONE) {
    // 通话抽屉显示时
    // console.log('表单项', '姓名', 'name', val.name)
    // console.log('表单项', '年龄', 'age', val.age)
    updateFormClientNameAndAge(val)
  }
}, { deep: true })
/**
 * 表单收集校验
 */
const validFormRecord = () => {
  // 根据表单设置（数据结构）校验必要项是否已填写
  const result: boolean = formSetting.value.every((settingItem: CollectionFormItem) => {
    // 必要
    if (settingItem?.requiredField) {
      const item = formContent.value.find((contentItem: FormRecordItemContent) => {
        return contentItem?.formCollectionId === settingItem?.id
      })
      // 表单项内容为空，返回false，否则返回true
      return !!item?.content
    }
    // 非必要
    return true
  })

  // 更新跟进状态可选列表
  updateFollowUpStatusList(result)
}
/**
 * 表单收集更新
 */
const onUpdateFormRecordContent = (data: FormRecordItemContent[]) => {
  formContent.value = data
  validFormRecord()
}

// ---------------------------------------- 表单收集 结束 ----------------------------------------

// ---------------------------------------- 客户跟进 开始 ----------------------------------------

// 客户跟进 默认值
class ClientFollow {
  star = false
  followUpStatus = FollowUpStatusEnum['跟进中']
  nextFollowUpTime = ''
  note = ''
}

// 客户跟进表单
const clientForm = reactive(new ClientFollow())
// 跟进状态可选列表
const followUpStatusList = ref<{ name: string, value: any }[]>([])
// 下次跟进时间快捷选择列表
const nextFollowUpTimePresets = ref<{ label: string, value: dayjs.Dayjs }[]>([
  { label: '4小时后', value: dayjs().add(4, 'hour') },
  { label: '1天后', value: dayjs().add(1, 'day') },
  { label: '2天后', value: dayjs().add(2, 'day') },
  { label: '1周后', value: dayjs().add(1, 'week') },
])

/**
 * 客户跟进 点击星标
 */
const onClickStar = () => {
  clientForm.star = !clientForm.star
}
/**
 * 更新跟进状态可选列表
 * @param {boolean} allowSuccess 允许选择跟进成功
 */
const updateFollowUpStatusList = (allowSuccess: boolean = false) => {
  if (allowSuccess) {
    followUpStatusList.value = [
      { name: '跟进中', value: FollowUpStatusEnum['跟进中'] },
      { name: '跟进成功', value: FollowUpStatusEnum['跟进成功'] },
      { name: '跟进失败', value: FollowUpStatusEnum['跟进失败'] },
    ]
  } else {
    // 如果当前状态是跟进成功，则应该立马修改成跟进中
    if (clientForm.followUpStatus === FollowUpStatusEnum['跟进成功']) {
      clientForm.followUpStatus = FollowUpStatusEnum['跟进中']
    }
    followUpStatusList.value = [
      { name: '跟进中', value: FollowUpStatusEnum['跟进中'] },
      { name: '跟进失败', value: FollowUpStatusEnum['跟进失败'] },
    ]
  }
}
/**
 * 跟进状态 下拉框切换显示隐藏
 * @param {boolean} visible 显示隐藏
 */
const onFollowUpStatusVisibleChange = async (visible: boolean) => {
  if (!visible) {
    // 下拉框从显示到隐藏时
  } else {
    // 下拉框从隐藏到显示时
    await nextTick()
    validFormRecord()
  }
}
/**
 * 禁止选择的日期
 * @param {Dayjs} current 选择的日期
 * @returns {boolean} 是否禁用
 */
const disabledDate = (current: Dayjs): boolean => {
  // 禁止选择现在和过去的时间，只允许选择未来的时间
  return current < dayjs().endOf('d').subtract(1, 'd')
}
/**
 * 客户跟进 下次跟进时间 输入框的值变化
 * @param {Date} val Date对象
 * @param {string} valStr 格式化日期
 */
const onChangeNextFollowUpTime = (val: Date, valStr: string) => {
  if (val && dayjs(val).isBefore(dayjs())) {
    return ElMessage.warning('请选择合适的下次跟进时间!')
  } else {
    clientForm.nextFollowUpTime = valStr
  }
}
/**
 * 更新客户跟进表单数据，从线索里获取
 * @param clue 线索信息
 */
const updateClientFormFromClue = (clue: ClueItem) => {
  clientForm.star = clue.star || false
  clientForm.followUpStatus = clue.latestFollowUpStatus || FollowUpStatusEnum['跟进中']
  clientForm.nextFollowUpTime = clue.nextFollowUpTime || ''
  clientForm.note = clue.latestFollowUpNote || ''
}
// 监听坐席状态，若从空闲变成通话状态，则更新一次客户跟进表单数据
watch(seatStatus, (val: SeatStatusEnum) => {
  if (val === SeatStatusEnum.MANUAL_DIRECT_DIALING || val === SeatStatusEnum.HUMAN_MACHINE_DIALING) {
    updateClientFormFromClue(currentClue.value || {})
  }
})

// ---------------------------------------- 客户跟进 结束 ----------------------------------------

// ---------------------------------------- 实时通话 开始 ----------------------------------------

// -------------------- 通话记录 开始 --------------------

// 通话记录
const callRecordList = ref<RecordDialogueData[]>([])
// 正在加载 更新实时通话记录
const loadingUpdateCallRecord = ref<boolean>(false)
// 节流锁 更新实时通话记录
const throttleUpdateCallRecord = new Throttle(loadingUpdateCallRecord)
// 定时器 更新实时通话记录
let timerUpdateCallRecord: number | null = null

interface StartEndInfoType {
  dialogTime: string,
  content: string
}

// 通话记录时间线
const startEndInfo = ref<StartEndInfoType[]>([])

/**
 * 更新通话记录时间线
 * @param {TaskCallRecordItem} res 当前通话信息
 */
const updateStartEndInfo = (res: TaskCallRecordItem) => {
  startEndInfo.value = []
  res?.talkTimeStart && startEndInfo.value.push({ dialogTime: res?.talkTimeStart ?? '', content: '电话接通' })
  res?.startMonitorTime && startEndInfo.value.push({ dialogTime: res?.startMonitorTime ?? '', content: '开始监听' })
  res?.endMonitorTime && startEndInfo.value.push({ dialogTime: res?.endMonitorTime ?? '', content: '结束监听' })
  res?.startAnswerTime && startEndInfo.value.push({ dialogTime: res?.startAnswerTime ?? '', content: '开始接听' })
  res?.talkTimeEnd && startEndInfo.value.push({
    dialogTime: res?.talkTimeEnd ?? '',
    content: (findValueInEnum(res?.whoHangup, HangupEnum) || '') + '挂断通话'
  })
}
/**
 * 更新实时通话记录
 */
const updateCallRecord = async () => {
  // 节流锁上锁
  if (throttleUpdateCallRecord.check()) {
    return
  }
  throttleUpdateCallRecord.lock()

  // 人工直呼需要更新对话详情
  // 人机协同需要更新对话详情和AI通话记录
  // 其中，查询对话详情需要用到recordId和callId，查询AI通话记录需要用到recordId
  // 这里按通话类型（人工直呼、人机协同）分类讨论

  const callId = callInfo.value.callId
  if (!callId) {
    // 节流锁解锁
    throttleUpdateCallRecord.unlock()
    return
  }

  // handleError(dialogErr, '更新对话详情', '更新人工直呼对话详情失败', { callId, recordId })
  /**
   * 接口错误统一处理公共函数
   * @param err 错误信息，一般是接口报错信息，JSON对象
   * @param action 名称（动作名称）
   * @param desc 描述（具体什么动作出现错误）
   * @param params 参数（接口报错时，请求参数params和响应结果response）
   */
  const handleError = (err: any, action: string, desc: string, params: any) => {
    console.error(desc, err)
    seatPhoneStore.report({
      type: SeatLogTypeEnum['错误'],
      action,
      desc,
      params,
      response: err,
    })
    trace({
      page: desc,
      params: {
        params,
        response: err,
      },
    })
  }

  if (callType.value === SeatCallTypeEnum.DIRECT) {
    // 人工直呼

    const recordId = seatPhoneStore.callRecordForManualDirect.recordId
    if (!recordId) {
      // 节流锁解锁
      throttleUpdateCallRecord.unlock()
      return
    }

    // 更新对话详情
    const [dialogErr, dialogRes] = <[any, RecordDialogueData[]]>await to(seatCallRecordModel.getManualDialogueDataList({
      callId,
      recordId,
    }))
    if (!dialogErr) {
      callRecordList.value = dialogRes.length ? dialogRes : []
    } else {
      handleError(dialogErr, '更新对话详情', '更新人工直呼对话详情失败', { callId, recordId })
    }

  } else {
    // 人机协同

    const recordId = seatPhoneStore.callRecordForHumanMachine.recordId
    if (!recordId) {
      // 节流锁解锁
      throttleUpdateCallRecord.unlock()
      return
    }

    // 更新对话详情
    const [dialogErr, dialogRes] = <[any, RecordDialogueData[]]>await to(seatCallRecordModel.getMixDialogueDataList({
      callId,
      recordId,
    }))
    if (!dialogErr) {
      callRecordList.value = dialogRes.length ? dialogRes : []
    } else {
      handleError(dialogErr, '更新对话详情', '更新人机协同对话详情失败', { callId, recordId })
    }

    // 更新AI通话记录
    const [aiRecordErr, aiRecordRes] = <[any, TaskCallRecordItem]>await to(seatCallRecordModel.getCallRecordMix({
      recordId,
    }))
    if (!aiRecordErr) {
      const data: TaskCallRecordItem = {}
      Object.assign(data, seatPhoneStore.callRecordForHumanMachine, aiRecordRes)
      updateAiRecord(data)
      // 根据AI通话记录，更新对话详情里的时间线
      updateStartEndInfo(aiRecordRes)
    } else {
      handleError(aiRecordErr, '更新通话记录', '更新AI通话记录失败', { recordId })
    }

  }

  // 节流锁解锁
  throttleUpdateCallRecord.unlock()
}
/**
 * 开启定时器 更新实时通话记录
 */
const startTimerUpdateCallRecord = () => {
  if (timerUpdateCallRecord) {
    return
  }
  timerUpdateCallRecord = <number><unknown>setInterval(() => {
    updateCallRecord()
    // 如果关闭通话抽屉，就同时关闭定时器
    if (seatPage.value !== SeatPageEnum.PHONE) {
      stopTimerUpdateCallRecord()
    }
  }, 1000)
}
/**
 * 关闭定时器 更新实时通话记录
 */
const stopTimerUpdateCallRecord = () => {
  if (!timerUpdateCallRecord) {
    return
  }
  clearInterval(timerUpdateCallRecord)
  timerUpdateCallRecord = null
}

// 监听坐席状态，若处于通话状态，则定时更新实时通话记录
watch(seatStatus, (val: SeatStatusEnum) => {
  if (
    val === SeatStatusEnum.MANUAL_DIRECT_DIALING
    || val === SeatStatusEnum.HUMAN_MACHINE_WINDOW
    || val === SeatStatusEnum.HUMAN_MACHINE_LISTEN
    || val === SeatStatusEnum.HUMAN_MACHINE_DIALING
  ) {
    startTimerUpdateCallRecord()
  } else {
    stopTimerUpdateCallRecord()
  }
}, { immediate: true })

// -------------------- 通话记录 结束 --------------------

// -------------------- 按钮 开始 --------------------

// 人工直呼，刚接通时禁止挂断的秒数
const MANUAL_DIRECT_DISABLE_HANGUP_SECOND = 3

// 挂断按钮 是否显示
const hangupVisible = computed(() => {
  // 人工直呼 通话
  if (callType.value === SeatCallTypeEnum.DIRECT) {
    return seatStatus.value === SeatStatusEnum.MANUAL_DIRECT_DIALING
  }
  // 人机协同 介入
  if (callType.value === SeatCallTypeEnum.MONITOR || callType.value === SeatCallTypeEnum.ANSWER) {
    return seatStatus.value === SeatStatusEnum.HUMAN_MACHINE_DIALING
  }
  return false
})
// 挂断按钮 是否禁用
const hangupDisabled = computed(() => {
  // 人工直呼，通话时长小于一定秒数时禁用
  if (
    seatPhoneStore.callType === SeatCallTypeEnum.DIRECT
    && seatStatus.value === SeatStatusEnum.MANUAL_DIRECT_DIALING
  ) {
    return 0 <= seatPhoneStore.receptionSecond && seatPhoneStore.receptionSecond < MANUAL_DIRECT_DISABLE_HANGUP_SECOND
  }
  return false
})
// 挂断按钮 展示文本
const hangupText = computed(() => {
  // 人工直呼，通话时长小于一定秒数时禁用
  if (
    seatPhoneStore.callType === SeatCallTypeEnum.DIRECT
    && seatStatus.value === SeatStatusEnum.MANUAL_DIRECT_DIALING
  ) {
    if (0 <= seatPhoneStore.receptionSecond && seatPhoneStore.receptionSecond < MANUAL_DIRECT_DISABLE_HANGUP_SECOND) {
      let leftSecond = MANUAL_DIRECT_DISABLE_HANGUP_SECOND - seatPhoneStore.receptionSecond
      return `(${leftSecond}s)`
    }
    return ''
  }
  return ''
})
// 监听按钮（人机协同监听模式）
const monitorVisible = computed(() => {
  // 人机协同 来电监听弹窗
  if (callType.value === SeatCallTypeEnum.MONITOR) {
    // 如果开启了自动监听/接听，则隐藏监听按钮
    if (seatSetting.value.autoAccept) {
      return false
    }
    return seatStatus.value === SeatStatusEnum.HUMAN_MACHINE_WINDOW
  }
  return false
})
// 接听按钮（人机协同接听模式）
const acceptVisible = computed(() => {
  // 人机协同 来电接听弹窗
  if (callType.value === SeatCallTypeEnum.ANSWER) {
    // 如果开启了自动监听/接听，则隐藏接听按钮
    if (seatSetting.value.autoAccept) {
      return false
    }
    return seatStatus.value === SeatStatusEnum.HUMAN_MACHINE_WINDOW
  }
  return false
})
// 介入按钮 是否显示
const interveneVisible = computed(() => {
  // 人机协同 监听
  if (callType.value === SeatCallTypeEnum.MONITOR || callType.value === SeatCallTypeEnum.ANSWER) {
    // 监听中，或者监听到介入后延迟启用介入按钮
    return seatStatus.value === SeatStatusEnum.HUMAN_MACHINE_LISTEN && !delayInterveneEnabled.value
  }
  return false
})
// 介入按钮 是否禁用
const interveneDisabled = computed(() => {
  // 通话没有建立正常的语音通道时，不能介入
  return seatPhoneStore.iceState !== 'connected' && seatPhoneStore.iceState !== 'completed'
})
// 提交按钮 是否显示
const submitVisible = computed(() => {
  // 人工直呼 话后处理
  if (callType.value === SeatCallTypeEnum.DIRECT) {
    return seatStatus.value === SeatStatusEnum.MANUAL_DIRECT_POSTING
  }
  // 人机协同 话后处理
  if (callType.value === SeatCallTypeEnum.MONITOR || callType.value === SeatCallTypeEnum.ANSWER) {
    return seatStatus.value === SeatStatusEnum.HUMAN_MACHINE_POSTING
  }
  return false
})
// 提交并拨打下一个按钮 是否显示
const submitAndNextVisible = computed(() => {
  // 人工直呼 话后处理
  if (callType.value === SeatCallTypeEnum.DIRECT) {
    // 签入任务为空，才是真正的人工直呼的工作模式
    // 签入任务不为空，是人机协同的工作模式，临时进行人工直呼，不需要提供拨打下一个
    return seatStatus.value === SeatStatusEnum.MANUAL_DIRECT_POSTING && !seatTaskList.value.length
  }
  return false
})
// 退出监听按钮 是否显示
const exitMonitorVisible = computed(() => {
  // 人机协同 监听
  if (callType.value === SeatCallTypeEnum.MONITOR || callType.value === SeatCallTypeEnum.ANSWER) {
    return seatStatus.value === SeatStatusEnum.HUMAN_MACHINE_LISTEN
  }
  return false
})
// 麦克风静音按钮 是否显示
const muteVisible = computed(() => {
  // 人工直呼 通话
  if (callType.value === SeatCallTypeEnum.DIRECT) {
    return seatStatus.value === SeatStatusEnum.MANUAL_DIRECT_DIALING
  }
  // 人机协同 介入
  if (callType.value === SeatCallTypeEnum.MONITOR || callType.value === SeatCallTypeEnum.ANSWER) {
    return seatStatus.value === SeatStatusEnum.HUMAN_MACHINE_DIALING
  }
  return false
})
// 发送短信按钮 是否显示
const sendSmsVisible = computed(() => {
  // 人工直呼 通话 话后处理
  if (callType.value === SeatCallTypeEnum.DIRECT) {
    return seatStatus.value === SeatStatusEnum.MANUAL_DIRECT_DIALING
      || seatStatus.value === SeatStatusEnum.MANUAL_DIRECT_POSTING
  }
  // 人机协同 介入 接听 话后处理
  if (callType.value === SeatCallTypeEnum.MONITOR || callType.value === SeatCallTypeEnum.ANSWER) {
    return seatStatus.value === SeatStatusEnum.HUMAN_MACHINE_DIALING
      || seatStatus.value === SeatStatusEnum.HUMAN_MACHINE_POSTING
  }
  return false
})
/**
 * 发送短信按钮 样式名
 */
const getSendSmsButtonClassName = computed(() => {
  if (seatStatus.value === SeatStatusEnum.MANUAL_DIRECT_DIALING
    || seatStatus.value === SeatStatusEnum.HUMAN_MACHINE_DIALING) {
    return 'dialing-send-sms-button'
  } else {
    return ''
  }
})

// 提交按钮 正在加载
const loadingSubmit = ref<boolean>(false)
// 提交按钮 节流锁
const throttleSubmit = new Throttle(loadingSubmit)

/**
 * 点击挂断按钮
 */
const onClickHangup = () => {
  seatPhoneStore.report({
    type: SeatLogTypeEnum['详细'],
    action: SeatLogActionEnum['坐席页面交互'],
    desc: '点击挂断按钮',
  })
  // 显示挂断电话弹窗
  dialogHangupVisible.value = true
}
/**
 * 点击提交按钮
 * @param callNext 提交成功后自动拨打下一个
 */
const onClickSubmit = async (callNext: boolean = false) => {
  seatPhoneStore.report({
    type: SeatLogTypeEnum['详细'],
    action: SeatLogActionEnum['坐席页面交互'],
    desc: callNext ? '点击话后处理提交并拨打下一个按钮' : '点击话后处理提交按钮',
  })
  // 节流锁上锁
  if (throttleSubmit.check()) {
    ElMessage.warning('正在提交，请稍候')
    return
  }
  throttleSubmit.lock()

  // 延迟请求接口
  await new Promise((resolve) => {
    setTimeout(resolve, 1000)
  })

  // 关闭倒计时
  stopProcessTimer()

  try {
    // 提交表单收集
    await seatFormRecordModel.saveFormRecord({
      fromCollectionContentList: formContent.value,
      callSeatId: currentClue.value.callSeatId,
      clueId: currentClue.value.id,
      formId: seatPhoneStore.formId ?? undefined,
    })

    // 提交跟进记录和人工记录

    // 公共参数生成函数
    const generateParams = () => {
      return {
        clueId: currentClue.value.id,
        followUpStatus: clientForm.followUpStatus,
        intentionClass: humanRecord.value.intentionClass ?? undefined,
        intentionTagIds: humanRecord.value.intentionTagIds ?? undefined,
        intentionTags: humanRecord.value.intentionTags ?? undefined,
        isPostingOutOfTime: processSecond.value <= 0,
        isStar: clientForm.star ?? false,
        nextFollowUpTime: clientForm.nextFollowUpTime,
        note: clientForm.note,
      }
    }

    // 错误处理函数
    const handleError = (err: any = null, action: string = '', desc: string = '', phone: string = '', params: any = {}) => {
      ElMessage.error('提交失败')
      seatPhoneStore.report({
        type: SeatLogTypeEnum['错误'],
        action,
        desc,
        phone,
        params,
        response: err,
      })
      trace({
        page: desc,
        params: {
          params,
          response: err,
        },
      })
      // 节流锁解锁
      throttleSubmit.unlock()
    }

    // 按通话类型提交
    if (seatStatus.value === SeatStatusEnum.MANUAL_DIRECT_POSTING) {
      // 人工直呼
      const params = { ...generateParams() }
      const [err, _] = <[Error | null, SeatCallResponse]>await to(seatWorkbenchCallModel.submitProcessManualDirectCall(params))
      if (err) {
        return handleError(
          err,
          '话后处理提交',
          '人工直呼 话后处理 提交失败',
          currentClue.value.phone || '',
          params
        )
      }
    } else if (seatStatus.value === SeatStatusEnum.HUMAN_MACHINE_POSTING) {
      // 人机协同
      const params = {
        ...generateParams(),
        recordId: seatPhoneStore.callRecordForHumanMachine?.recordId ?? '',
      }
      const [err, _] = <[Error | null, SeatCallResponse]>await to(seatWorkbenchCallModel.submitProcessHumanMachine(params))
      if (err) {
        return handleError(
          err,
          '话后处理提交',
          '人机协同 话后处理 提交失败',
          seatPhoneStore.callRecordForHumanMachine.phone || '',
          params
        )
      }
    }

    // 节流锁解锁
    throttleSubmit.unlock()
    // 接口正常响应
    ElMessage({
      message: '提交成功',
      type: 'success',
    })

    // 提交成功后清空客户状态记录
    seatPhoneStore.resetEventList()

    seatPhoneStore.report({
      action: callType.value === SeatCallTypeEnum.DIRECT ?
        SeatLogActionEnum['人工直呼-话后处理提交']
        : SeatLogActionEnum['人机协同-话后处理提交'],
      desc: callType.value === SeatCallTypeEnum.DIRECT
        ? '人工直呼 话后处理 提交成功'
        : '人机协同 话后处理 提交成功',
      phone: callType.value === SeatCallTypeEnum.DIRECT
        ? currentClue.value.phone
        : seatPhoneStore.callRecordForHumanMachine.phone,
    })

    // 坐席空闲状态
    seatInfoStore.updateSeatStatusByTaskList()
    // 通话类型
    seatPhoneStore.updateCallType()

    // 关闭通话抽屉，显示线索界面
    seatInfoStore.updateSeatPage(SeatPageEnum.CLUE)

    // 清空当前线索
    seatPhoneStore.updateCurrentClue({})
    // 清空通话记录
    callRecordList.value = []

    // 清空表单
    resetForm()
    // 清除通话信息
    seatPhoneStore.clearCallInfo()

    // 更新线索模块
    emits('update')

    console.log('是否拨打下一个 callNext', callNext)
    // 人工直呼工作模式中，是否拨打下一个
    // 签入任务为空，才是真正的人工直呼的工作模式
    // 签入任务不为空，是人机协同的工作模式，临时进行人工直呼，不需要提供拨打下一个
    if (!seatTaskList.value.length && callNext) {
      // await seatPhoneStore.callNext('手动')
      seatPhoneStore.needCallNext = '手动'
    }
  } catch (e) {
    // 节流锁解锁
    throttleSubmit.unlock()
  }
}
/**
 * 点击人机协同监听按钮
 */
const onClickMonitor = () => {
  seatPhoneStore.report({
    type: SeatLogTypeEnum['详细'],
    action: SeatLogActionEnum['坐席页面交互'],
    desc: '点击人机协同监听按钮',
  })
  seatPhoneStore.handleAccept(false)
}
/**
 * 点击人机协同接听按钮
 */
const onClickAccept = () => {
  seatPhoneStore.report({
    type: SeatLogTypeEnum['详细'],
    action: SeatLogActionEnum['坐席页面交互'],
    desc: '点击人机协同接听按钮',
  })
  seatPhoneStore.handleAccept(false)
}
/**
 * 点击人机协同介入按钮
 */
const onClickIntervene = () => {
  seatPhoneStore.report({
    type: SeatLogTypeEnum['详细'],
    action: SeatLogActionEnum['坐席页面交互'],
    desc: '点击人机协同介入按钮',
  })
  seatPhoneStore.intervene()
}
/**
 * 点击人机协同退出监听按钮
 */
const onClickExitMonitor = () => {
  seatPhoneStore.report({
    type: SeatLogTypeEnum['详细'],
    action: SeatLogActionEnum['坐席页面交互'],
    desc: '点击人机协同退出监听按钮',
  })
  // 显示退出监听弹窗，需要填写并提交未介入原因后，才能真正退出监听
  dialogExitMonitorVisible.value = true
}
/**
 * 点击静音按钮
 */
const onClickMute = () => {
  // 切换麦克风静音开关
  const oldMuted: boolean = muted.value ?? false
  muted.value = seatPhoneStore.switchLocalAudioMuted(!oldMuted) ?? false
  seatPhoneStore.report({
    type: SeatLogTypeEnum['详细'],
    action: SeatLogActionEnum['坐席页面交互'],
    desc: '点击静音按钮 切换到 ' + (muted.value ? '静音' : '有声'),
  })
}
// 监听坐席状态，从其他状态转变成话后处理时，开启定时器
watch(
  seatStatus,
  (val) => {
    if (val === SeatStatusEnum.HUMAN_MACHINE_POSTING || val === SeatStatusEnum.MANUAL_DIRECT_POSTING) {
      startProcessTimer()
    } else {
      stopProcessTimer()
    }
  }
)
/**
 * 点击发送短信按钮
 */
const onClickSendSms = () => {
  seatPhoneStore.report({
    type: SeatLogTypeEnum['详细'],
    action: SeatLogActionEnum['坐席页面交互'],
    desc: '点击发送短信按钮 显示发送短信弹窗',
  })
  // 显示发送短信弹窗
  if (callType.value === SeatCallTypeEnum.DIRECT) {
    // 人工直呼
    // 接口参数
    smsStore.sendSmsDialogForm.clueId = currentClue.value.id ?? undefined
    smsStore.sendSmsDialogForm.callRecordId = seatPhoneStore.callRecordForManualDirect.recordId ?? undefined
    // 页面展示
    smsStore.sendSmsDialogForm.phoneNumber = currentClue.value.clueUniqueId ?? undefined
    // 组件数据
    smsStore.sendSmsDialogForm.type = FollowUpTypeEnum['人工直呼']
  } else {
    // 人机协同
    // 接口参数
    smsStore.sendSmsDialogForm.clueId = currentClue.value.id ?? undefined
    smsStore.sendSmsDialogForm.callRecordId = seatPhoneStore.callRecordForHumanMachine.recordId ?? undefined
    // 页面展示
    smsStore.sendSmsDialogForm.phoneNumber = seatPhoneStore.callRecordForHumanMachine.recordId ?? undefined
    // 组件数据
    smsStore.sendSmsDialogForm.type = FollowUpTypeEnum['人机协同']
  }
  smsStore.showSendSmsDialog()
}

// 话后处理计时器
const processTimer = ref<number | undefined>(undefined)
// 话后处理当前剩余秒数
const processSecond = ref(60)

/**
 * 开启话后处理倒计时
 */
const startProcessTimer = () => {
  // 不要重复启动计时器
  if (processTimer.value !== undefined) {
    return
  }
  // 秒数重置
  processSecond.value = totalPostSecond.value
  // 设置倒计时
  processTimer.value = <number><unknown>setInterval(() => {
    processSecond.value--
    if (processSecond.value <= 0) {
      stopProcessTimer()
    }
  }, 1000)
}
/**
 * 关闭话后处理倒计时
 */
const stopProcessTimer = () => {
  // 关闭倒计时
  clearTimeout(processTimer.value)
  processTimer.value = undefined
}

// -------------------- 按钮 结束 --------------------

// ---------------------------------------- 实时通话 结束 ----------------------------------------

// ---------------------------------------- AI记录 开始 ----------------------------------------

// AI记录
const aiRecord = ref<TaskCallRecordItem>({})

/**
 * 更新AI记录
 * @param record 新的记录信息
 */
const updateAiRecord = (record: TaskCallRecordItem) => {
  aiRecord.value = record
}

// ---------------------------------------- AI记录 结束 ----------------------------------------

// ---------------------------------------- 人工记录 开始 ----------------------------------------

// 子组件人工记录
const humanRecord = ref<SeatCallParam>({})

/**
 * 子组件更新人工记录
 * @param {SeatCallParam} val 坐席接口参数
 */
const updateHumanRecord = (val: SeatCallParam) => {
  humanRecord.value = val
}

// ---------------------------------------- 人工记录 结束 ----------------------------------------

// ---------------------------------------- 退出监听弹窗 开始 ----------------------------------------

// 退出监听 正在加载
const loadingExitMonitor = ref<boolean>(false)
// 退出监听 节流锁
const throttleExitMonitor = new Throttle(loadingExitMonitor)

// 退出监听理由
const exitMonitorReason = ref('')
// 退出监听 取消按钮 是否显示
const exitMonitorCancelButtonVisible = computed(() => {
  // 人机协同监听中可以显示
  // 其余情况隐藏
  return seatStatus.value === SeatStatusEnum.HUMAN_MACHINE_LISTEN
})

/**
 * 退出监听弹窗 关闭
 */
const closeDialogExitMonitor = () => {
  dialogExitMonitorVisible.value = false
}
/**
 * 退出监听弹窗 点击取消按钮
 */
const onClickCancelExitMonitor = () => {
  seatPhoneStore.report({
    type: SeatLogTypeEnum['详细'],
    action: SeatLogActionEnum['坐席页面交互'],
    desc: '退出监听弹窗 点击取消按钮',
  })
  closeDialogExitMonitor()
  // 节流锁解锁
  throttleExitMonitor.unlock()
}
/**
 * 退出监听弹窗 点击确定按钮
 */
const onClickConfirmExitMonitor = async () => {
  // 节流锁上锁
  if (throttleExitMonitor.check()) {
    return
  }
  throttleExitMonitor.lock()

  seatPhoneStore.report({
    type: SeatLogTypeEnum['详细'],
    action: SeatLogActionEnum['坐席页面交互'],
    desc: '退出监听弹窗 点击确定按钮',
  })
  if (!exitMonitorReason.value) {
    ElMessage({
      message: '请填写未接听/介入理由',
      type: 'warning',
    })
    // 节流锁解锁
    throttleExitMonitor.unlock()
    return
  }

  if (seatPage.value === SeatPageEnum.PHONE) {
    // 挂断电话
    await seatPhoneStore.hangup()
  }

  try {
    // 退出监听，提交退出监听理由
    await seatWorkbenchCallModel.failHumanMachine({
      recordId: seatPhoneStore.callRecordForHumanMachine?.recordId,
      isMiss: false,
      noReceptionReason: exitMonitorReason.value + '----' + userStore.account,
    })
    ElMessage({
      message: '提交成功',
      type: 'success',
    })
    seatPhoneStore.report({
      action: callType.value === SeatCallTypeEnum.ANSWER
        ? SeatLogActionEnum['人机协同-退出接管']
        : SeatLogActionEnum['人机协同-退出监听'],
      desc: callType.value === SeatCallTypeEnum.ANSWER
        ? '未接听原因提交成功'
        : '未介入原因提交成功',
      phone: callType.value === SeatCallTypeEnum.DIRECT
        ? currentClue.value.phone
        : seatPhoneStore.callRecordForHumanMachine.phone,
    })
    // 关闭弹窗
    closeDialogExitMonitor()
    // 清空原因
    exitMonitorReason.value = ''
    // 关闭通话抽屉，回到线索列表
    seatInfoStore.updateSeatPage(SeatPageEnum.CLUE)
    // 回到人机协同空闲状态
    seatInfoStore.updateSeatStatus(SeatStatusEnum.HUMAN_MACHINE_IDLE)
    // 清空通话信息
    seatPhoneStore.clearCallInfo()
    // 退出监听成功后清空客户状态记录
    await seatPhoneStore.resetEventList()
  } catch (e) {
    seatPhoneStore.report({
      type: SeatLogTypeEnum['错误'],
      action: callType.value === SeatCallTypeEnum.ANSWER
        ? SeatLogActionEnum['人机协同-退出接管']
        : SeatLogActionEnum['人机协同-退出监听'],
      desc: callType.value === SeatCallTypeEnum.ANSWER
        ? '未接听原因提交失败'
        : '未介入原因提交失败',
      phone: callType.value === SeatCallTypeEnum.DIRECT
        ? currentClue.value.phone
        : seatPhoneStore.callRecordForHumanMachine.phone,
    })
  } finally {
    // 节流锁解锁
    throttleExitMonitor.unlock()
  }
}
/**
 * 退出监听弹窗 点击快捷选项
 * @param text
 */
const onClickExitMonitorShortcut = (text: string = '') => {
  seatPhoneStore.report({
    type: SeatLogTypeEnum['详细'],
    action: SeatLogActionEnum['坐席页面交互'],
    desc: '退出监听弹窗 点击快捷选项 ' + text,
  })
  // 将文本输入到文本框内
  exitMonitorReason.value += text
}

// ---------------------------------------- 退出监听弹窗 结束 ----------------------------------------

// ---------------------------------------- 挂断电话弹窗 开始 ----------------------------------------

// 挂断电话弹窗 是否显示
const dialogHangupVisible = ref(false)

/**
 * 挂断电话弹窗 关闭
 */
const closeDialogHangup = () => {
  dialogHangupVisible.value = false
}
/**
 * 挂断电话弹窗 点击取消按钮
 */
const onClickCancelDialogHangup = () => {
  seatPhoneStore.report({
    type: SeatLogTypeEnum['详细'],
    action: SeatLogActionEnum['坐席页面交互'],
    desc: '挂断电话弹窗 点击取消按钮',
  })
  closeDialogHangup()
}
/**
 * 挂断电话弹窗 点击确定按钮
 */
const onClickConfirmDialogHangup = () => {
  seatPhoneStore.report({
    type: SeatLogTypeEnum['详细'],
    action: SeatLogActionEnum['坐席页面交互'],
    desc: '挂断电话弹窗 点击确定按钮',
  })
  seatPhoneStore.hangup()
  closeDialogHangup()
}

// ---------------------------------------- 挂断电话弹窗 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

/**
 * 初始化
 */
const init = () => {
  // 话后处理时长，初始值
  processSecond.value = totalPostSecond.value
}
/**
 * 重置
 */
const reset = async () => {
  // 清空坐席信息
  stopProcessTimer()
  // 清空表单
  resetForm()
  // 页面卸载时清空客户状态记录
  seatPhoneStore.resetEventList()
}
onMounted(() => {
  init()
  window.addEventListener('beforeunload', reset)
})
onBeforeUnmount(() => {
  reset()
  window.removeEventListener('beforeunload', reset)
})

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
.workbench-box {
  overflow: hidden;
  width: auto;
  min-width: 960px;
  height: 100%;
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  grid-template-rows: 1fr 1fr;
  grid-template-areas:
    "left-top-section center-section right-section"
    "left-bottom-section center-section right-section";
  grid-gap: 0;
  padding: 0;
  background-color: #f2f3f5;
  &.hide-left-section {
    grid-template-columns: 2fr 1fr;
    grid-template-rows: 100%;
    grid-template-areas:
    "center-section right-section"
  }
  & > * {
    position: relative;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    width: 100%;
    min-width: 300px;
    height: 100%;
    background-color: #fff;
    margin: 0;
    border-radius: 4px;
  }
}
.drawer-header {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  flex-shrink: 0;
  width: 100%;
  height: 56px;
  padding: 0 16px;
  border-bottom: 1px solid #EBEDF0;
  background: #FFF;
  color: var(--primary-black-color-600);
  font-size: 16px;
  font-weight: 600;
  text-align: left;
}
.drawer-header-title {
  flex: none;
}
.header-button {
  height: auto;
  margin-left: 12px;
  padding: 10px;
  font-size: 13px;
}
.drawer-header-time {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: nowrap;
  flex: none;
  &.reception-time {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    flex-wrap: nowrap;
    flex: none;
    width: 160px;
    margin-left: 12px;
    padding: 2px 4px;
    border-radius: 4px;
    background-color: #13BF77;
    color: #FFF;
    font-size: 13px;
    line-height: 20px;
  }
}
.drawer-header-info {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: nowrap;
  flex: none;
  font-size: 13px;
  .info-name {
    font-weight: bold;
    color: #000;
    margin-left: 8px;
  }
  .info-phone {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: nowrap;
    margin-left: 8px;
    font-weight: bold;
    color: #165DFF;
  }
}
.drawer-header-button {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  margin: 0 0 0 auto;
}
.section-header {
  flex: none;
  padding: 8px 12px;
  border-bottom: 1px solid #E1E3E6;
  background: #FFF;
  color: #313233;
  font-size: 14px;
  font-weight: 600;
}
.left-top-section {
  grid-area: left-top-section;
  text-align: left;
}
.left-bottom-section {
  grid-area: left-bottom-section;
  text-align: left;
  .el-form-item {
    margin-bottom: 12px;
  }
}
.center-section {
  grid-area: center-section;
  display: flex;
  flex-direction: column;
  text-align: center;
}
.right-section {
  grid-area: right-section;
  text-align: left;
}
:deep(.section-main) {
  padding: 0 16px;
  background-color: #fff;
}
:deep(.el-form-item__label) {
  padding-right: 0;
}
:deep(.client-form-datetime.el-input) {
  width: 100%;
  min-width: 200px;
}
:deep(.client-form-datetime .el-input__wrapper) {
  width: 100%;
  min-width: 200px;
  margin-right: 16px;
}
:deep(.client-form-datetime .el-input__prefix) {
  display: none;
}
.workbench-call-record {
  position: relative;
  width: 100%;
  height: 100%;
}
.workbench-button-box {
  position: absolute;
  bottom: 0;
  left: 0;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: stretch;
  flex-wrap: nowrap;
  gap: 8px;
  width: 100%;
  padding: 20px 0;
  background-color: transparent;
}
.workbench-button {
  position: relative;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  flex: auto;
  overflow: hidden;
  width: 250px;
  max-width: 250px;
  padding: 8px 10px;
  border-radius: 4px;
  font-size: 14px;
  line-height: 22px;
  cursor: pointer;
  user-select: none;
  /* 按钮半透明遮罩 用于悬浮交互 */
  &::after {
    content: "";
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
  /* 按钮悬浮时 亮色交互 */
  &.hover-light {
    &:hover::after {
      background-color: rgba(255, 255, 255, .3);
    }
    &:active::after {
      background-color: rgba(0, 0, 0, .1);
    }
  }
  /* 按钮悬浮时 暗色交互 */
  &.hover-dark {
    &:hover::after {
      background-color: rgba(0, 0, 0, .1);
    }
    &:active::after {
      background-color: rgba(0, 0, 0, .05);
    }
  }
  /* 按钮色系 蓝色 */
  &.button-blue {
    border: 1px solid #165DFF;
    background-color: #165DFF;
    color: #fff;
  }
  /* 按钮色系 白色 */
  &.button-white {
    border: 1px solid #13BF77;
    background-color: #fff;
    color: #13BF77;
  }
  /* 按钮色系 绿色 */
  &.button-green {
    border: 1px solid #13BF77;
    background-color: #13BF77;
    color: #fff;
    /* 禁用 */
    &.disabled {
      cursor: not-allowed;
    }
  }
  /* 加载图标 */
  .loading-icon {
    margin-right: 8px;
    /* 顺时针旋转 */
    animation: clockwise-rotate 2s linear infinite;
    @keyframes clockwise-rotate {
      0% {
        transform: rotate(0deg);
      }
      50% {
        transform: rotate(180deg);
      }
      100% {
        transform: rotate(360deg);
      }
    }
  }
  /* 通话时的发送短信按钮 */
  &.dialing-send-sms-button {
    max-width: 80px;
    margin-right: 24px;
    margin-left: auto;
  }
}
</style>
