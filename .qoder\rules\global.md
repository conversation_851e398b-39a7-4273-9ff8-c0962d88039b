---
trigger: always_on
alwaysApply: true
description: Vue3 前端开发规范
---
# 🏗️ 项目结构

```
ai-report/
├── .git/                     # Git 版本控制目录
├── .cursor/                  # Cursor 编辑器相关配置
├── .vscode/                  # VSCode 配置
├── dist/                     # 构建输出目录
├── node_modules/             # 依赖包目录
├── public/                   # 静态资源目录
├── src/                      # 源代码目录（核心）
│   ├── api/                  # API 接口定义
│   ├── assets/               # 静态资源（图片、图标等）
│   ├── axios/                # Axios 封装
│   ├── components/           # Vue 组件
│   ├── router/               # 路由配置
│   ├── store/                # 状态管理（Pinia）
│   ├── type/                 # TypeScript 类型定义
│   ├── utils/                # 工具函数
│   ├── views/                # 页面视图
│   │   ├── error-pages/      # 错误页面（404，403等）
│   │   ├── layout/           # 布局（侧边栏、主题、头部等）
│   │   ├── Login.vue         # 登录页面
│   │   ├── merchant/         # 商户端页面
│   │   └── operator/         # 运营端页面
│   ├── App.vue               # 根组件
│   ├── main.ts               # 应用入口
│   └── ...                   # 其他配置文件
├── .gitignore                # Git 忽略规则
├── build.js                  # 构建脚本
├── env.d.ts                  # 环境变量类型声明
├── index.html                # 入口 HTML
├── package.json              # 项目配置和依赖
├── postcss.config.cjs        # PostCSS 配置
├── tailwind.config.cjs       # Tailwind CSS 配置
├── tsconfig.json             # TypeScript 配置
├── tsconfig.config.json      # 扩展 TypeScript 配置
└── vite.config.ts            # Vite 构建配置
```

# 🎨 技术栈

**Vue3 + TypeScript + Pinia + Element Plus + Tailwind CSS + Axios + Vue Router**

---

# 🚀 AI 代码生成规范

## ⚠️ 任务执行流程

### 1. 二次确认机制
- **任务理解确认**：详细分析用户需求，确认功能点和实现方案
- **影响范围确认**：明确需要修改的文件和代码范围
- **风险评估确认**：评估对现有代码逻辑的潜在影响

### 2. 修改范围控制
- **文件清单**：明确列出需要创建、修改、删除的文件
- **功能边界**：确保修改内容不超出需求范围
- **依赖关系**：检查文件间的依赖关系，避免破坏现有逻辑

### 3. 代码保护原则
- **最小修改原则**：只修改必要的代码片段
- **逻辑完整性**：确保原有业务逻辑不被破坏
- **功能兼容性**：新增功能不影响现有功能正常运行

### 4. 文档同步更新
- **目录结构检查**：代码生成后检查是否需要更新 md 文件中的目录结构
- **文档一致性**：确保文档与实际代码结构保持一致
- **规范更新**：如有新的开发模式，及时更新相关规范文档


# 📝 编码规范

## 📝 注释规范

### 注释原则
- **普通函数和变量**：无需添加注释（代码自解释）
- **复杂功能**：必须添加详细注释说明

### 需要注释的场景
```typescript
// ❌ 不需要注释的简单情况
const userName = 'admin'
const getUserName = () => userName

// ✅ 需要注释的复杂情况
/**
 * 处理复杂的数据转换逻辑
 * @param data 原始数据
 * @param options 转换配置项
 * @returns 转换后的数据结构
 */
const transformComplexData = (data: any[], options: TransformOptions) => {
  // 复杂的数据处理逻辑
  return processedData
}

/**
 * 业务复杂的组件状态管理
 * 涉及多个异步操作和状态同步
 */
const handleComplexBusinessLogic = async () => {
  // 步骤1: 数据预处理
  // 步骤2: 异步请求处理
  // 步骤3: 状态更新和同步
}
```

## 🛠️ 工具函数使用规范

### Utils 使用原则
- **优先使用**：项目已有的 `src/utils` 中的通用方法
- **避免重复**：开发前先检查是否已有相似功能的工具函数
- **合理封装**：将可复用的逻辑抽取为工具函数

### 项目已有工具函数示例
```typescript
// 数据处理工具
import { formatDate, exportExcel, deepClone } from '@/utils/utils'

// WebSocket 通信
import { handleWebSocket } from '@/utils/websocket'

// 座席管理工具
import { seatUtils } from '@/utils/seat'

// 使用示例
const formattedDate = formatDate(new Date()) // ✅ 使用日期格式化
const exportData = exportExcel(tableData) // ✅ 使用导出功能
const clonedData = deepClone(originalData) // ✅ 使用深拷贝
```

## 👨‍💻 组件结构标准

```vue
<template>
  <!-- Template content -->
</template>

<script setup lang="ts">
// Vue core imports
import { ref, computed, onMounted } from 'vue'

// Third-party libraries
import { storeToRefs } from 'pinia'

// Internal imports
import { useUserStore } from '@/store/user'
import type { User } from '@/types/user'

// Props definition
const props = defineProps<{
  userId: string
}>()

// Component logic
</script>

<style lang="postcss" scoped>
/* Component styles */
</style>
```



## 🔷 TypeScript 使用规范

- 对于接口类型需要在 `src/type` 中查找对应的 `interface`、`type` 或 `enum`
- 如果不存在，需要自行创建；如果存在则直接引入使用

## 🌐 API 接口规范

### 接口创建规则

1. 分析 `src/api` 中是否已有相关接口
2. 如果不存在，需要自行创建；如果存在则直接引入
3. 创建新接口文件需要遵循以下模板：

```typescript
import { cancelRequest, http } from '@/axios'
import { } from '@/type/'

export const aiOutboundTaskModel = {
  importPhones: (data: any) => {
    return http({
      data,
      url: "AiSpeech/aiOutboundTask/importPhonesFromExcel",
      method: "POST",
    }).then(res => res as unknown)
  },
}
```

### 接口调用规范

**必须使用以下格式进行接口调用：**

```typescript
const [err, res] = await to(smsUrlModel.saveNormal(params))
if (err) {
  // 错误处理
  return
}
// 成功处理
```

## 🗃️ 状态管理 (Pinia)

```typescript
import { defineStore } from 'pinia'
import { MerchantAccountInfo, } from '@/type/merchant'

// 当前选中的商户信息
const currentMerchant: MerchantInfo = {}

export const useMerchantStore = defineStore({
  id: 'merchant',
  state() {
    return {
      currentMerchant,
      // ...
    }
  },
  actions: {
    // ...
  },
  persist: [
    {
      storage: sessionStorage,
    }
  ]
})
```

## 🚦 路由配置

### 添加路由规则

1. 在 `src/router/asyncRoute/index.ts` 中添加路由
2. **务必区分商户端和运营端**（与目录位置对应）

```typescript
import MerchantBlacklist from '@/views/merchant/system-setting/Blacklist/Index.vue'

{
  path: '/merchant/system-setting/blacklist',
  name: 'MerchantBlacklist',
  meta: {
    id: routeMap['商户黑名单'].id,
    type: 2,
    title: routeMap['商户黑名单'].name,
  },
  component: MerchantBlacklist
}
```

### 权限配置

在 `route-map.ts` 中添加页面权限：

```typescript
'商户黑名单': {
  id: '206-2',
  name: '黑名单设置',
  isMerchant: true,
  permissions: {}
},
```

## 🎨 样式规范

### 样式优先级

1. **优先使用** `src/index.pcss` 中的全局样式/全局颜色/全局类等
2. **复杂且复用性高的样式** 在组件的 `<style>` 中添加 class

### 响应式要求

- 确保样式能够在**不同尺寸的电脑端**正常运行
- 适配主流分辨率和屏幕尺寸

## 🔐 权限管理

### 权限配置流程

1. 在 `src/router/asyncRoute/route-map` 中添加权限定义
2. 在具体的按钮或指定位置添加权限限制逻辑

## 📊 常量管理

### 常量存放位置

- **全局常量**：`src/assets/js/constant.ts` 和 `src/utils/constant.ts`
- **局部常量**：各组件/页面同级目录下的 `constant.ts`

### 使用规范

使用常量时（包括选项、初始化数据、表单规则、CSS class 等）：

1. **优先搜索** 全局和局部常量是否已存在
2. 如不存在，在合适位置新增并引入
3. **必须从 constant 文件引入**，避免硬编码


## ⚠️ 错误处理与异步操作

### 异步错误处理规范

对于以下场景的错误处理：
- 接口调用
- 表单校验
- 二次确认
- 异步转同步操作

**必须使用 `await-to-js` 的 `to` 方法**，减少 `try-catch` 的使用【务必遵守】

⚠️ **严格禁止事项**：
- **禁止使用 `try-catch`**：除非遇到特殊情况（如第三方库强制要求），否则一律使用 `to` 方法
- **禁止使用 `console`**：代码中不得出现任何 `console.log`、`console.error` 等调试语句

```typescript
import { to } from 'await-to-js'

const [err, result] = await to(asyncOperation())
if (err) {
  // 错误处理逻辑
  return
}
// 成功处理逻辑
```

---


# 🔍 代码审核规范

## 📋 审核检查清单

### 1. 正确性与逻辑
- **功能预期**：代码是否按预期工作？是否存在潜在Bug？
- **边界条件**：是否考虑了边界条件和异常情况？
- **逻辑完整性**：业务逻辑是否完整合理？
- **数据流**：数据传递和处理是否正确？

### 2. 可读性与维护性
- **命名规范**：变量、函数、组件命名是否清晰易懂？
- **注释质量**：是否有必要的注释说明复杂逻辑？
- **代码结构**：是否有效封装组件和函数？
- **输入输出**：函数和组件的输入输出是否合理？
- **代码复用**：是否充分利用现有工具函数和组件？

### 3. 性能优化
- **性能瓶颈**：是否存在性能瓶颈？能否进一步优化？
- **冗余计算**：是否存在不必要的计算和变量？
- **冗余引入**：是否有多余的import和依赖？
- **资源清理**：是否有效清理定时器、监听器和数据？
- **内存泄漏**：是否可能造成内存泄漏？

### 4. 安全性与健壮性
- **安全漏洞**：是否存在XSS、注入等安全漏洞？
- **输入验证**：是否对用户输入进行适当验证？
- **异常处理**：是否能应对恶意输入或异常情况？
- **数据敏感性**：是否正确处理敏感数据？

### 5. 编码规范
- **语言规范**：是否符合TypeScript/Vue3社区推荐的编码规范？
- **项目规范**：是否遵循项目内部的编码规范？
- **一致性**：代码风格是否与项目保持一致？

# 🔧 Git 命令执行规范

## 📌 核心原则
- **快速退出**：任何分页显示使用 `q` 立即退出
- **最少命令**：优先使用最简单有效的Git命令
- **安全操作**：危险命令前先备份当前分支

---

# 📋 开发检查清单

## 代码提交前检查

### 📝 代码质量检查
- 确认原有逻辑未被破坏
- 确认新增功能符合规范
- 确认注释添加合理
- 确认使用了合适的工具函数
- 确认文档结构是否需要更新
- 确认代码格式符合项目规范

### 🔍 代码审核检查
- 通过正确性与逻辑检查
- 通过可读性与维护性检查
- 通过性能优化检查
- 通过安全性与健壮性检查
- 通过编码规范检查

### 🔧 Git操作检查
- 确认提交信息清晰明确
- 确认没有提交不必要的文件
- 确认分支策略符合团队规范
- 确认代码已通过必要的测试
