import { computed, ref } from 'vue'
import { useScriptTrainStore } from '@/store/script-train'
import {
  InterruptTypeEnum,
  ScriptTextTrainAiInterruptEnum,
  ScriptTextTrainInfo,
  ScriptTrainStatusEnum,
  ScriptTrainTypeEnum
} from '@/type/speech-craft'
import { scriptTrainTextModel } from '@/api/speech-craft'
import { RecordDialogueData } from '@/type/task'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Throttle } from '@/utils/utils'
import to from 'await-to-js'
import { useScriptStore } from '@/store/script'
import dayjs from 'dayjs'
import { updateCorpusConfig } from '@/views/operator/ai-resource/MainProgressManager/Train/common'

// ---------------------------------------- 通用 开始 ----------------------------------------

const scriptStore = useScriptStore()
const scriptTrainStore = useScriptTrainStore()

// 电话号码
export const textTrainPhone = ref<string>('')
// 通话ID
export const textTrainCallId = ref<string>('')

/**
 * 重置文字训练所有数据
 */
export const resetTextTrainData = () => {
  textTrainPhone.value = ''
  textTrainCallId.value = ''
  msgVal.value = ''
  currentAiInterruptSecond.value = 0
  aiInterruptStatus.value = ScriptTextTrainAiInterruptEnum.NOT
  lastAiIndex.value = -1
  lastAiItem.value = lastAiItemDefault()
}

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 开关 开始 ----------------------------------------

/**
 * 关闭电话
 */
const stopTextTrainCall = async () => {
  try {
    // 挂断电话
    await scriptTrainTextModel.hangupCall({
      callId: textTrainCallId.value
    })
    hangup()
  } catch (e) {
    console.error('挂断电话时出错', e)
    ElMessage.error('挂断电话时出错')
  }
}
/**
 * 开始电话
 */
const initTextTrainCall = async () => {
  try {
    const res = <ScriptTextTrainInfo[]>await scriptTrainTextModel.initCall({
      scriptLongId: scriptStore.id
    })
    // 是否成功开始电话
    const success = (res ?? []).some((item: ScriptTextTrainInfo) => {
      // 更新必要数据
      item?.phone && (textTrainPhone.value = item?.phone ?? '')
      item?.callId && (textTrainCallId.value = item?.callId ?? '')
      // 如果失败，则catch里会清空这些数据的
      return item?.success
    })
    if (!success || !res?.length) {
      throw new Error()
    }
    // 更新训练状态
    scriptTrainStore.status = ScriptTrainStatusEnum.IN_CALL
    // 开始训练计时
    scriptTrainStore.openTrainTimer()
    // 更新对话详情
    handleTextTrainResponse(res)
  } catch (e: any) {
    // 结束文字训练
    endTextTrain()
    // 提示错误
    let errStr = e?.message ?? '开始训练接口发生错误'
    if (e?.data?.message) {
      errStr = '开始训练接口发生错误：' + e?.data?.message
    }
    ElMessageBox.alert(errStr, '无法开始文字训练', {
        confirmButtonText: '确定',
      },
    ).then(() => {
    }).catch(() => {
    })
  }
}
/**
 * 结束文字训练
 */
export const endTextTrain = () => {
  // 停止正在播放的音频
  scriptTrainStore.clearAudio = true
  // 重置状态
  scriptTrainStore.resetStatus()
  // 重置文字训练所有数据
  resetTextTrainData()
  // 关闭检查话术定时器
  scriptTrainStore.closeCheckingTimer()
  // 关闭训练计时
  scriptTrainStore.closeTrainTimer()
  // 关闭AI可打断定时器
  closeAiInterruptTimer()
  resetAiInterruptTimer()
  // 更新训练历史
  scriptTrainStore.needUpdate.history = true
  // 更新对话详情
  scriptTrainStore.needUpdate.dialog = true
  // 更新通话记录
  scriptTrainStore.needUpdate.record = true

  ElMessage.warning('已结束文字训练')
}
/**
 * 开始文字训练
 */
export const startTextTrain = async () => {
  // 重置状态
  scriptTrainStore.resetStatus()
  // 训练类型改为文字训练
  scriptTrainStore.trainType = ScriptTrainTypeEnum.TEXT
  // 开始检查话术
  await scriptTrainStore.launchCheckScript(initTextTrainCall, endTextTrain)
}
/**
 * 挂断电话
 */
const hangup = () => {
  // 关闭训练计时
  scriptTrainStore.closeTrainTimer()
  setTimeout(() => {
    // 电话挂断
    ElMessage({
      message: '电话已挂断',
      type: 'warning',
    })
    // 更新状态为挂断电话
    scriptTrainStore.status = ScriptTrainStatusEnum.HANG_UP
  }, 500)
}

// ---------------------------------------- 开关 结束 ----------------------------------------

// ---------------------------------------- 事件 开始 ----------------------------------------

/**
 * 提交事件
 */
export const submitTextTrainEvent = async () => {
  if (!scriptTrainStore.eventSelectedList.length) {
    ElMessage({
      message: '没有选择任何事件',
      type: 'warning'
    })
    return
  }

  try {
    const res = <ScriptTextTrainInfo[]>await scriptTrainTextModel.submitTextTrainEvent({
      phone: textTrainPhone.value ?? '',
      stage: scriptTrainStore.eventSelectedList.join(',') ?? ''
    })
    handleTextTrainResponse(res)
    ElMessage({
      message: '事件提交成功',
      type: 'success'
    })
  } catch (e) {
    ElMessage({
      message: '事件提交失败',
      type: 'error'
    })
  }
}

// ---------------------------------------- 事件 结束 ----------------------------------------

// ---------------------------------------- 对话详情 开始 ----------------------------------------

// 最后一条AI语句索引
export const lastAiIndex = ref(-1)
// 最后一条AI语句信息默认值
const lastAiItemDefault = (): ScriptTextTrainInfo => {
  return {
    id: -1,
    audioFileUrl: '',
    audioFinishWaitTime: 0,
    callId: '',
    content: '',
    corpusName: '',
    dialogTime: '',
    finished: false,
    hitBranch: '',
    hitIntention: '',
    hitPhrase: '',
    hitSemantic: '',
    interruptType: InterruptTypeEnum['不允许打断'],
    phone: '',
    success: false,
    type: 0,
    unitContentName: '',
  }
}
// 最后一条AI语句信息
const lastAiItem = ref<ScriptTextTrainInfo>(lastAiItemDefault())

/**
 * 更新最后一条AI语句
 * @param {boolean} updateTimer 是否更新定时器
 */
const updateLastAiItem = (updateTimer: boolean = true) => {
  lastAiIndex.value = -1
  scriptTrainStore.dialogData.forEach((item: RecordDialogueData, index: number) => {
    // 标记最后一条AI语音
    if (item.type === 0) {
      lastAiIndex.value = index ?? -1
    }
  })

  if (lastAiIndex.value > -1) {
    lastAiItem.value = JSON.parse(JSON.stringify(scriptTrainStore.dialogData[lastAiIndex.value]))
  }

  if (!updateTimer) {
    return
  }

  // 最后的AI语句是否可以打断
  if (lastAiItem.value.interruptType === InterruptTypeEnum['不允许打断']) {
    // 不允许打断，关闭定时器
    closeAiInterruptTimer()
    resetAiInterruptTimer()
  } else {
    // 允许打断，启动定时器
    openAiInterruptTimer()
  }
}
/**
 * 更新对话详情
 * @param {ScriptTextTrainInfo[]} list 追加更新的对话详情
 */
const updateTextTrainDialogList = (list: ScriptTextTrainInfo[]) => {
  if (!list?.length) {
    return
  }

  const dialogList: RecordDialogueData[] = (list ?? []).map((item: ScriptTextTrainInfo) => {
    const newItem: RecordDialogueData = { ...item } as RecordDialogueData
    newItem.content = newItem.content?.replace(/\s/g, '') || ''
    newItem.urls = [item.audioFileUrl ?? '']
    // 格式化日期时间前，验证日期时间字符串的格式是否正确
    const datetime = dayjs(item.dialogTime)
    newItem.dialogTime = datetime.isValid() ? datetime.format('YYYY-MM-DD HH:mm:ss') : 'N/A'
    return newItem
  })
  // 追加到对话内容里
  // @ts-ignore
  scriptTrainStore.dialogData.push(...dialogList)
  // 更新对话详情里的AI语料打断设置
  updateCorpusConfig(scriptTrainStore.dialogData).then(() => {
  }).catch(() => {
  })
}
/**
 * 处理接口响应数据
 * @param {ScriptTextTrainInfo[]} res 接口响应数据
 */
const handleTextTrainResponse = (res?: ScriptTextTrainInfo[]) => {
  // 更新对话详情
  if (res?.length) {
    updateTextTrainDialogList(res)
  }
  // 如果有挂断标记，立马挂断电话，但是继续播放最后一句AI音频，不用结束训练
  const finished = (res ?? []).some((item: ScriptTextTrainInfo) => {
    return !!item?.finished || false
  }) || false

  // 如果新的对话详情只有1句，说明是以下三种情况：
  // 1. 开场的AI一句话，
  // 2. 是客户的一句话，但没有触发AI回复，
  // 当前AI音频保持继续播放，可打断定时器保持继续运行。
  // 3. AI在多语句语料中的一句话，
  // 播放新的AI音频，可打断定时器重置计时并运行。

  // 如果新的对话详情有2句，说明客户一句，AI一句，一轮对话。
  // 需要判断新的AI这句话的打断类型，然后更新对话详情里的打断类型和定时器。

  if (res?.length === 1) {
    // 新对话只有1句
    if (res[0]?.type === 0) {
      // AI
      // 更新最后一条AI语句
      updateLastAiItem()
    } else if (res[0]?.type === 1) {
      // 客户
      // 更新最后一条AI语句
      updateLastAiItem(false)
    }
  } else if (res?.length === 2) {
    // 根据是否是挂断电话的最后一句，决定是否停止正在播放的旧语句
    scriptTrainStore.clearAudio = !finished
    // 更新最后一条AI语句
    updateLastAiItem()
  }

  if (finished) {
    hangup()
  }
}
/**
 * 更新组件clearAudio属性
 */
export const onUpdateClearAudio = async (val: boolean = false) => {
  scriptTrainStore.clearAudio = val
  if (val && !multipleCorpusFinished.value) {
    // AI说完并且是多语句语料中的一句话
    // AI自动说下句话
    await onClickStopAiInterrupt()
  }
}

// ---------------------------------------- 对话详情 结束 ----------------------------------------

// ---------------------------------------- 对话详情 AI可打断 开始 ----------------------------------------

// AI可打断是否显示
export const aiInterruptVisible = computed(() => {
  return scriptTrainStore.trainType === ScriptTrainTypeEnum.TEXT
    && (
      scriptTrainStore.status === ScriptTrainStatusEnum.IN_CALL
      || scriptTrainStore.status === ScriptTrainStatusEnum.HANG_UP
    )
    && scriptTrainStore.aiInterruptEnabled
})

// AI可打断定时器ID
let aiInterruptTimer: any = null
// AI可打断定时器当前时长，单位秒
export const currentAiInterruptSecond = ref(0)
// AI可打断定时器最大时长，单位秒
export const maxAiInterruptSecond = ref(45)
// 当前AI语音打断状态
export const aiInterruptStatus = ref<ScriptTextTrainAiInterruptEnum>(ScriptTextTrainAiInterruptEnum.NOT)

// watch(aiInterruptStatus, (val) => {
//   console.log('aiInterruptStatus', val)
// }, { immediate: true })
// watch(currentAiInterruptSecond, (val) => {
//   console.log('currentAiInterruptSecond', val)
// }, { immediate: true })
// watch(maxAiInterruptSecond, (val) => {
//   console.log('maxAiInterruptSecond', val)
// }, { immediate: true })

/**
 * 重置AI可打断定时器
 */
const resetAiInterruptTimer = () => {
  // 默认最大可打断时长
  maxAiInterruptSecond.value = 45
  // 将音频时长作为最大可打断时长
  const audio: HTMLAudioElement = new Audio()
  const url = lastAiItem.value?.audioFileUrl
  audio.src = url ? (url + '?rand=' + Math.random()) : ''
  // console.log('audio', audio.src)
  audio.addEventListener('loadedmetadata', () => {
    maxAiInterruptSecond.value = Math.ceil(audio.duration || 0)
    currentAiInterruptSecond.value = maxAiInterruptSecond.value
    // console.log('loadedmetadata', 'maxAiInterruptSecond', maxAiInterruptSecond.value, 'currentAiInterruptSecond', currentAiInterruptSecond.value)
  })
  aiInterruptStatus.value = ScriptTextTrainAiInterruptEnum.NOT
}
/**
 * 处理AI可打断定时器
 */
const handleAiInterruptTimer = async () => {
  if (currentAiInterruptSecond.value <= 0) {
    // 停止可打断
    closeAiInterruptTimer()
    resetAiInterruptTimer()
    // 停止播放当前音频
    scriptTrainStore.clearAudio = true
    // 超时，AI已说完
    aiInterruptStatus.value = ScriptTextTrainAiInterruptEnum.FINISH
  } else {
    // 计时递增
    currentAiInterruptSecond.value--
    aiInterruptStatus.value = ScriptTextTrainAiInterruptEnum.ALLOW
  }
}
/**
 * 关闭AI可打断定时器
 */
const closeAiInterruptTimer = () => {
  if (typeof aiInterruptTimer === 'number') {
    clearInterval(aiInterruptTimer)
  }
  aiInterruptTimer = null
}
/**
 * 开启AI可打断定时器
 */
const openAiInterruptTimer = () => {
  closeAiInterruptTimer()
  resetAiInterruptTimer()
  aiInterruptStatus.value = ScriptTextTrainAiInterruptEnum.ALLOW
  aiInterruptTimer = setInterval(handleAiInterruptTimer, 1000)
}
/**
 * 点击AI可打断的结束按钮
 */
export const onClickStopAiInterrupt = async () => {
  // 停止可打断
  closeAiInterruptTimer()
  resetAiInterruptTimer()
  // 停止播放当前音频
  scriptTrainStore.clearAudio = true
  // 更新状态为已说完
  aiInterruptStatus.value = ScriptTextTrainAiInterruptEnum.FINISH
  // 如果是多语句语料并且还没说完，AI自动说下一句话
  if (scriptTrainStore.status !== ScriptTrainStatusEnum.HANG_UP && !multipleCorpusFinished.value) {
    await continueMultipleCorpus()
  }
}
/**
 * 点击切换AI可打断开关
 * @param {boolean} val 新值
 */
export const onChangeAiInterruptEnabled = (val: boolean) => {
  // 关闭AI可打断
  if (!val) {
    // 立马停止当前正在播放语音的AI可打断定时器
    closeAiInterruptTimer()
    resetAiInterruptTimer()
  }
}

// ---------------------------------------- 对话详情 AI可打断 结束 ----------------------------------------

// ---------------------------------------- 对话详情 多语句语料 开始 ----------------------------------------

/**
 * 当前多语句语料是否都说完
 * 如果对话详情里，
 * AI语句没有等待时间，比如0或者null或者undefined等逻辑非，那客户只能调用多语句说话接口，用来模拟多语句的打断等情况
 * AI语句有等待时间，比如大于0，那客户可以打断、说话、沉默等正常接口，用来模拟这个语料说完后的情况
 */
export const multipleCorpusFinished = computed(() => {
  // AI语句等待时间
  return !!lastAiItem.value?.audioFinishWaitTime
})
/**
 * AI继续说多语句语料的下一句话
 */
const continueMultipleCorpus = async () => {
  try {
    const res = <[any, ScriptTextTrainInfo[]]>await scriptTrainTextModel.continueCorpus({
      callId: textTrainCallId.value,
    })
    handleTextTrainResponse(res)
  } catch (e) {
  }
}

// ---------------------------------------- 对话详情 多语句语料 结束 ----------------------------------------

// ---------------------------------------- 消息框 开始 ----------------------------------------

// 消息框占位符提示
export const msgEditorHint: string = 'Enter 发送，F9 (Fn+F9) 沉默，Esc 清空，F8 (Fn+F8) 挂断'
// 消息框内容
export const msgVal = ref<string>('')
// 消息框内容，最大文本长度
export const msgValMaxLength: number = 100
// 正在发送文字
export const loadingSending = ref<boolean>(false)
// 正在发送文字，节流锁
export const throttleSending = new Throttle(loadingSending)
// 正在发送沉默
export const loadingSilence = ref<boolean>(false)
// 正在发送沉默，节流锁
export const throttleSilence = new Throttle(loadingSilence)

/**
 * 点击清空按钮
 */
export const onClickReset = () => {
  if (loadingSending.value || loadingSilence.value) {
    return
  }
  msgVal.value = ''
}
/**
 * 点击沉默按钮
 */
export const onClickSilence = async () => {
  // 节流锁上锁
  if (throttleSilence.check()) {
    return
  }
  throttleSilence.lock()
  console.log('点击沉默按钮')

  const [err, res] = <[any, ScriptTextTrainInfo[]]>await to(scriptTrainTextModel.silence({
    callId: textTrainCallId.value,
  }))
  if (err) {
    ElMessage.error('沉默发送失败')
    // 节流锁解锁
    throttleSilence.unlock()
    return
  }
  // 节流锁解锁
  throttleSilence.unlock()
  handleTextTrainResponse(res)
}
/**
 * 点击发送按钮
 */
export const onClickSendMsg = async () => {
  // 空消息
  if (!msgVal.value) {
    ElMessage.warning('不能发送空消息')
    return
  }

  // 节流锁上锁
  if (throttleSending.check()) {
    return
  }
  throttleSending.lock()
  console.log('点击发送按钮', msgVal.value)

  try {
    let res: ScriptTextTrainInfo[] = []

    if (multipleCorpusFinished.value) {
      // 多语句语料已说完

      if (aiInterruptStatus.value === ScriptTextTrainAiInterruptEnum.ALLOW) {
        // AI可打断，客户打断说话
        res = <ScriptTextTrainInfo[]>await scriptTrainTextModel.interrupt({
          callId: textTrainCallId.value,
          words: msgVal.value ?? ''
        })
      } else {
        // AI没被打断，或者AI已经说完了，客户正常接着说话
        res = <ScriptTextTrainInfo[]>await scriptTrainTextModel.sendMsg({
          callId: textTrainCallId.value,
          words: msgVal.value ?? ''
        })
      }

    } else {
      // 多语句语料还没说完

      if (aiInterruptStatus.value !== ScriptTextTrainAiInterruptEnum.NOT) {
        // AI可打断，客户打断说话
        res = <ScriptTextTrainInfo[]>await scriptTrainTextModel.interrupt({
          callId: textTrainCallId.value,
          words: msgVal.value ?? ''
        })
      } else {
        // AI不可打断，提示
        ElMessage.warning('AI不可打断')
      }

    }

    // 节流锁解锁
    throttleSending.unlock()
    if (res) {
      msgVal.value = ''
      handleTextTrainResponse(res)
    }
  } catch (e) {
    ElMessage.error('消息发送失败')
  } finally {
    // 节流锁解锁
    throttleSending.unlock()
  }
}
/**
 * 点击挂断按钮
 */
export const onClickHangup = async () => {
  await stopTextTrainCall()
}
/**
 * 文本框按键事件
 */
export const onMsgInputKeyEvent = async (event: KeyboardEvent) => {
  // console.log(event.key, event.code, event.ctrlKey)
  if (event.key === 'Enter') {
    // 发送
    await onClickSendMsg()
  } else if (event.key === 'F9') {
    // 沉默
    await onClickSilence()
  } else if (event.key === 'Escape') {
    // 清空
    onClickReset()
  } else if (event.key === 'F8') {
    // 挂断
    await onClickHangup()
  } else if (event.key === 'F7') {
    // 结束AI说话
    await onClickStopAiInterrupt()
  }
}

// ---------------------------------------- 消息框 结束 ----------------------------------------
