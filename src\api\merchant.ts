import { http } from '@/axios'
import {
  MerchantInfo,
  MerchantLineInfo,
  MerchantLineInfoParams,
  MerchantLineRegionApiParams,
  MerchantProjectItem,
  MerchantScriptInfoParams,
  MerchantSetting,
  MerchantSupplyLineParams,
  SmsTemplateItem,
  MerchantSmsTemplateItem,
  SmsTemplateParams,
  SmsVariableItem,
  SmsChannelItem,
  SmsAccountGroupItem,
  ChannelWarnConfig, SmsChannelStatusParams,
} from '@/type/merchant'
import { AxiosResponse } from 'axios'
import { SmsAccountItem } from '@/type/sms';
import { filterEmptyParams } from '@/utils/utils';

export const merchantModel = {
  // ---------------------------------------- 商户信息 开始 ----------------------------------------

  // 查询商户列表
  getMerchantList: async () => {
    return http({
      url: '/AiSpeech/tenant/findList',
      method: 'POST',
    }).then(res => res as unknown)
  },
  // 查询商户列表
  getMerchantListByCondition: async (params?: {
    account?: string,
    accountName?: string,
    tenantName?: string
  }) => {
    return http({
      url: '/AiSpeech/tenant/findTenantList',
      method: 'POST',
      params: params ? filterEmptyParams(params) : {},
    }).then(res => res as unknown)
  },
  // 查询火山商户列表
  getVolcanoMerchantList: async () => {
    return http({
      url: '/AiSpeech/tenant/findVolcanoList',
      method: 'POST',
    }).then(res => res as unknown)
  },
  // 保存商户（创建和编辑）
  saveMerchant: async (data: MerchantInfo) => {
    return http({
      url: '/AiSpeech/tenant/save',
      method: 'POST',
      data
    }).then(res => res as unknown)
  },

  // ---------------------------------------- 商户信息 结束 ----------------------------------------

  // ---------------------------------------- 商户账号 开始 ----------------------------------------

  // 放在user.ts中统一管理所有账号相关接口

  // ---------------------------------------- 商户账号 结束 ----------------------------------------

  // ---------------------------------------- 商户线路 开始 ----------------------------------------

  // 按商户账号查询商户线路列表
  getMerchantLineListById: async (data: {
    groupId: string,
    scriptId?: number,
    lineType: string
  }) => {
    return http({
      url: '/AiSpeech/callLineTenant/findActiveTenantLinesByGroupId',
      method: 'GET',
      data
    }).then(res => res as unknown)
  },
  // 按条件查询商户线路列表（商户端使用）
  getMerchantLineListByCondition: async (data: MerchantLineInfoParams) => {
    return http({
      url: '/AiSpeech/callLineTenant/findTenantLinesByConditions',
      method: 'POST',
      data
    }).then(res => res as unknown as Pick<MerchantLineInfo, 'lineName' | 'lineNumber' | 'lineType' | 'enableStatus'>[])
  },
  // 按条件查询商户线路列表（运营端使用）
  getMerchantLineListByConditionForOperation: async (data: MerchantLineInfoParams) => {
    return http({
      url: '/AiSpeech/callLineTenant/findTenantLinesByConditionsForOperation',
      method: 'POST',
      data: filterEmptyParams(data),
    }).then(res => res as unknown as MerchantLineInfo[])
  },
  // 添加商户线路
  addLine: async (data: MerchantLineInfoParams) => {
    return http({
      url: '/AiSpeech/callLineTenant/addOneTenantLine',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  // 编辑商户线路
  editLine: async (data: MerchantLineInfoParams) => {
    return http({
      url: '/AiSpeech/callLineTenant/editOneTenantLine',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  // 修改商户线路启用状态
  changeLineStatus: async (params: MerchantLineInfoParams) => {
    return http({
      url: '/AiSpeech/callLineTenant/changeTenantLineEnableStatus',
      method: 'POST',
      params,
    }).then(res => res as unknown)
  },
  // 某条商户线路，供应线路列表
  getSupplierLineList: async (data: MerchantLineInfoParams) => {
    return http({
      url: '/AiSpeech/callLineTenant/findSupplyLinesInTenantLine',
      method: 'GET',
      data
    }).then(res => res as unknown)
  },
  // 某条商户线路，可添加供应线路列表
  getSupplierAvailableLineList: async () => {
    return http({
      url: '/AiSpeech/callLineSupply/findAllSupplyLines',
      method: 'GET',
    }).then(res => res as unknown)
  },
  // 某条商户线路，调整供应线路优先级顺序
  changeSupplierLinePriority: async (data: MerchantLineInfoParams) => {
    return http({
      url: '/AiSpeech/callLineTenant/saveSupplyLineNumbersInTenantLine',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  // 某条商户线路，添加一条供应线路
  addSupplierLine: async (params: MerchantLineInfoParams) => {
    return http({
      url: '/AiSpeech/callLineTenant/addOneSupplyLineInTenantLine',
      method: 'POST',
      params,
    }).then(res => res as unknown)
  },
  // 某条商户线路，删除一条供应线路
  deleteSupplierLine: async (params: MerchantLineInfoParams) => {
    return http({
      url: '/AiSpeech/callLineTenant/deleteOneSupplyLineInTenantLine',
      method: 'DELETE',
      params,
    }).then(res => res as unknown)
  },
  // 某条商户线路，查看支持地区
  getSupportRegion: async (data: MerchantLineRegionApiParams) => {
    return http({
      url: '/AiMonitor/callLineChart/getCountryChartToday',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  // 根据线路编号查询线路信息
  getLineByNumber: async (params: { tenantLineNumber: string }) => {
    return http({
      url: '/AiMonitor/callLineTenantManager/findTenantLineByNumber',
      method: 'POST',
      params,
    }).then(res => res as unknown)
  },
  // 获取全部商户线路
  getAllMerchantLines: async () => {
    return http({
      url: '/AiSpeech/callLineTenant/findAllTenantLines',
      method: 'GET',
    }).then(res => res as unknown)
  },
  // 获取全部商户线路，筛选出当前商户、启用状态、指定的线路类型
  getActiveTenantLinesByGroupIdAndLineType: async (params: {
    groupId: string,
    lineType: string
  }) => {
    return http({
      url: '/AiSpeech/callLineTenant/findActiveTenantLinesByGroupIdAndLineType',
      method: 'GET',
      params,
    }).then(res => res as unknown)
  },

  // ---------------------------------------- 商户线路 结束 ----------------------------------------

  // ---------------------------------------- 商户话术 开始 ----------------------------------------

  // 获取当前商户账号已绑定的话术列表
  getScriptList: async (data: MerchantScriptInfoParams) => {
    return http({
      url: '/AiSpeech/tenant/related-script',
      method: 'GET',
      data
    }).then(res => res as unknown)
  },
  getAiScriptList: async (data: MerchantScriptInfoParams) => {
    return http({
      url: '/AiSpeech/tenant/related-ai-script',
      method: 'GET',
      data
    }).then(res => res as unknown)
  },
  getManualScriptList: async (data: MerchantScriptInfoParams) => {
    return http({
      url: '/AiSpeech/tenant/related-manual-script',
      method: 'GET',
      data
    }).then(res => res as unknown)
  },
  // 获取可关联的话术列表
  getScriptAvailableList: async (data: { status: string }) => {
    return http({
      url: '/AiSpeech/script/findAllScriptByStatus',
      method: 'GET',
      data
    }).then(res => res as unknown)
  },
  // 当前商户账号关联话术
  bindMerchantScript: async (data: { scriptId: number, tenantId: number, groupId: string, scriptStringId: string }) => {
    return http({
      url: '/AiSpeech/tenant/create-related-script',
      method: 'POST',
      data
    }).then(res => res as unknown)
  },
  // 当前商户账号解除关联话术
  unbindMerchantScript: async (data: {
    scriptId: number,
    tenantId: number,
    groupId: string,
    scriptStringId: string
  }) => {
    return http({
      url: '/AiSpeech/tenant/remove-related-script',
      method: 'POST',
      data
    }).then(res => res as unknown)
  },

  // ---------------------------------------- 商户话术 结束 ----------------------------------------

  // ---------------------------------------- 账号设置列表 开始 ----------------------------------------
  // 获取所有商户下的项目列表
  getAccountSetting: async (data: { accountId: number }) => {
    return http({
      url: '/AiSpeech/admin/findAccountOperatorParamByAccountId',
      method: 'GET',
      data,
    }).then(res => res as unknown as MerchantSetting)
  },
  saveAccountSetting: async (data: MerchantSetting) => {
    return http({
      url: '/AiSpeech/admin/updateAccountOperatorParam',
      method: 'POST',
      data: filterEmptyParams(data),
    }).then(res => res as unknown as MerchantSetting)
  },
  // ---------------------------------------- 账号设置列表 结束 ----------------------------------------
}

// 商户线路内的供应线路
export const merchantSupplyLineModel = {
  // 获取供应线路列表
  getList: async (data: MerchantSupplyLineParams) => {
    return http({
      url: '/AiSpeech/callLineTenant/getSupplyLinesInTenantLine',
      method: 'GET',
      data,
    }).then(res => res as unknown)
  },
  // 切换临停开关
  switchPendingStatus: async (data: MerchantSupplyLineParams) => {
    return http({
      url: '/AiSpeech/callLineTenant/changeTenantSupplyLinePendingStatus',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  // 批量切换临停开关: data: {商户线路编号_供应线路编号：boolean}
  batchSwitchPendingStatus: async (data: Record<string, boolean>) => {
    return http({
      url: '/AiSpeech/callLineTenant/changeTenantSupplyLinePendingStatusBatch',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  // 号码检测
  sampleTenantSupplyLine: async (data: {
    tenantLineNumber: string,
    supplyLineNumber: string,
  }) => {
    return http({
      url: '/AiMonitor/callLineSupplyManager/tenantSupplyLineSampling',
      method: 'POST',
      data,
    }).then(res => res as unknown as Record<string, any>)
  },

  // 切换是否优先线路
  switchPriorityStatus: async (data: MerchantSupplyLineParams) => {
    return http({
      url: '/AiSpeech/callLineTenant/changeTenantSupplyLinePriority',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  // 修改可支配并发
  changeMaxConcurrency: async (data: MerchantSupplyLineParams) => {
    return http({
      url: '/AiSpeech/callLineTenant/changeTenantSupplyLineLimit',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  // 修改可支配并发
  getTenantSupplyLineCache: async (data: {
    tenantLineNumber: string,
    supplyLineNumber: string,
  }) => {
    return http({
      url: '/AiMonitor/callLineSupplyManager/tenantSupplyLineCache',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
}

// 商户 项目
export const merchantProjectModel = {
  // 获取项目列表
  getProjectList: async (params: MerchantProjectItem) => {
    return http({
      url: '/AiSpeech/tenantProgramAdmin/findAllPrograms',
      method: 'POST',
      params,
    }).then(res => res as unknown)
  },
  // 编辑项目
  editProject: async (data: MerchantProjectItem) => {
    return http({
      url: '/AiSpeech/tenantProgramAdmin/editProgram',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  // 新增项目
  addProject: async (data: MerchantProjectItem) => {
    return http({
      url: '/AiSpeech/tenantProgramAdmin/saveProgram',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  // 获取所有商户下的项目列表
  getAllProjectList: async () => {
    return http({
      url: '/AiSpeech/tenantProgramAdmin/findAllProgramsForCallRecord',
      method: 'POST',
    }).then(res => res as unknown as MerchantProjectItem[])
  },
}

// 商户管理 短信模板
export const merchantSmsTemplateModel = {
  // 查询短信模板(商户端使用)
  getSmsTemplate: async (data: any) => {
    return http({
      url: '/AiSpeech/tenantSmsTemplate/findSmsTemplate',
      method: 'POST',
      data,
    }).then(res => res as unknown as MerchantSmsTemplateItem[])
  },
  // 查询短信模板(运营端端使用)
  getSmsTemplateForOperation: async (data: any) => {
    return http({
      url: '/AiSpeech/tenantSmsTemplate/findSmsTemplateForOperation',
      method: 'POST',
      data,
    }).then(res => res as unknown as SmsTemplateItem[])
  },
  // 新增短信模板
  addSmsTemplate: async (data: SmsTemplateParams) => {
    return http({
      url: '/AiSpeech/tenantSmsTemplate/addSmsTemplate',
      method: 'POST',
      data,
    }).then(res => res as unknown as SmsTemplateItem)
  },
  // 编辑短信模板
  editSmsTemplate: async (data: SmsTemplateParams) => {
    return http({
      url: '/AiSpeech/tenantSmsTemplate/editSmsTemplate',
      method: 'POST',
      data,
    }).then(res => res as unknown as SmsTemplateItem)
  },
  // 启用短信模板
  enableSmsTemplate: async (params: SmsTemplateParams) => {
    return http({
      url: '/AiSpeech/tenantSmsTemplate/startSmsTemplate',
      method: 'POST',
      params,
    }).then(res => res as unknown as AxiosResponse)
  },
  // 禁用短信模板
  disableSmsTemplate: async (params: SmsTemplateParams) => {
    return http({
      url: '/AiSpeech/tenantSmsTemplate/stopSmsTemplate',
      method: 'POST',
      params,
    }).then(res => res as unknown as AxiosResponse)
  },
  // 启用短信通道
  enableSmsChannel: async (params: SmsTemplateParams) => {
    return http({
      url: '/AiSpeech/tenantSmsTemplate/startSmsChannel',
      method: 'POST',
      params,
    }).then(res => res as unknown as AxiosResponse)
  },
  // 禁用短信通道
  disableSmsChannel: async (params: SmsTemplateParams) => {
    return http({
      url: '/AiSpeech/tenantSmsTemplate/stopSmsChannel',
      method: 'POST',
      params,
    }).then(res => res as unknown as AxiosResponse)
  },
  // 获取全量短信模板列表
  getTotalSmsTemplate: async () => {
    return http({
      url: '/AiSpeech/tenantSmsTemplate/findForDropDownList',
      method: 'GET',
    }).then(res => res as unknown as SmsTemplateItem[])
  },
}

// 商户管理 短信模板-短信通道
export const merchantSmsChannelModel = {
  // 修改短信通道挂起状态
  switchSmsChannelStatus: (params: SmsChannelStatusParams) => {
    return http({
      url: '/AiSpeech/tenantSmsChannel/changeTenantSmsChannelStatus',
      method: 'POST',
      params,
    }).then(res => res as unknown as string)
  },
  getSmsChannelByTemplateId: (data: {templateId: number}) => {
    return http({
      url: '/AiSpeech/tenantSmsChannel/findOneTenantSmsChannelByTemplateId',
      method: 'GET',
      data,
    }).then(res => res as unknown as SmsChannelItem)
  },
  // 短信供应商账号不再使用groupId限制选择
  // getAvailableSmsAccounts: (data: {groupId: string, secondIndustries: string[], enableStatus: string}) => {
  //   return http({
  //     url: '/AiSpeech/smsAccount/findSmsAccountsForSmsTemplate',
  //     method: 'POST',
  //     data,
  //   }).then(res => res as unknown as SmsAccountItem[])
  // },
  addEmptySmsChannel: (data: {templateId: number}) => {
    return http({
      url: '/AiSpeech/tenantSmsChannel/addOneEmptyTenantSmsChannel',
      method: 'GET',
      data,
    }).then(res => res as unknown)
  },
  saveSmsChannelAccountGroup: (data: {
    smsAccountGroup: SmsAccountGroupItem,
    templateId?: number
  }) => {
    return http({
      url: '/AiSpeech/tenantSmsChannel/saveOneSmsAccountGroup',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  deleteSmsChannelAccountGroup: (params: {
    smsAccountGroupId: number,
    templateId: number
  }) => {
    return http({
      url: '/AiSpeech/tenantSmsChannel/deleteOneSmsAccountGroup',
      method: 'POST',
      params,
    }).then(res => res as unknown)
  },
  // 临停通道下账号组中的某个账号
  changeSmsChannelAccountStatus: (params: {
    smsAccountNumber : string,
    smsAccountGroupId: number,
    status: boolean
    templateId: number
  }) => {
    return http({
      url: '/AiSpeech/tenantSmsChannel/changeSmsChannelAccountStatus',
      method: 'POST',
      params,
    }).then(res => res as unknown)
  },
  changeSmAccountStatus: (params: {
    smsAccountNumber : string,
    status: boolean
  }) => {
    return http({
      url: '/AiSpeech/smsAccount/changePendingStatus',
      method: 'POST',
      params,
    }).then(res => res as unknown)
  },
  saveChannelWarnConfig: (data: ChannelWarnConfig) => {
    return http({
      url: '/AiSpeech/tenantSmsChannel/editSmsChannelWarn',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
}


// 商户管理 短信变量
export const merchantSmsVariableModel = {
  // 查询短信变量
  getSmsVariable: async (params: any) => {
    return http({
      url: '/AiSpeech/tenantSmsVariable/findSmsVariable',
      method: 'GET',
      params,
    }).then(res => res as unknown as SmsVariableItem[])
  },
  // 新增短信变量
  addSmsVariable: async (data: any) => {
    return http({
      url: '/AiSpeech/tenantSmsVariable/addSmsVariable',
      method: 'POST',
      data,
    }).then(res => res as unknown as SmsVariableItem)
  },
  // 编辑短信变量
  editSmsVariable: async (data: any) => {
    return http({
      url: '/AiSpeech/tenantSmsVariable/editSmsVariable',
      method: 'POST',
      data,
    }).then(res => res as unknown as SmsVariableItem)
  },
  // 删除短信变量
  deleteSmsVariable: async (data: any) => {
    return http({
      url: '/AiSpeech/tenantSmsVariable/deleteSmsVariable',
      method: 'DELETE',
      data,
    }).then(res => res as unknown as AxiosResponse)
  },
}
