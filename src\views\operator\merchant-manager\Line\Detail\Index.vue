<template>
  <!--详情标题-->
  <HeaderBox :title="[{title: '商户管理'}, {title: currentMethod}]" />

  <!--详情主体-->
  <el-scrollbar class="submodule-detail" wrap-class="detail-main">
    <el-form
      ref="formRef"
      label-width="90px"
      label-position="right"
      inline
      :model="form"
      :rules="rules"
      :disabled="merchantStore.readonly"
    >
      <!--基本信息-->
      <div class="form-block">
        <div class="form-block-title">
          基本信息
        </div>
        <el-form-item label="线路编号：">
          <el-input
            v-model.trim="form.lineNumber"
            placeholder="创建成功后自动生成"
            clearable
            disabled
            style="width: 240px;"
          />
        </el-form-item>
        <el-form-item label="线路类型：" prop="lineType">
          <!--新建线路，允许修改，启用-->
          <!--复制线路，禁止修改，禁用-->
          <!--编辑线路，禁止修改，禁用-->
          <el-select
            v-model.trim="form.lineType"
            clearable
            placeholder="请选择线路类型"
            style="width: 240px;"
            :disabled="form.id! > -1 || form.id === -1 && form.supplyLineGroups && form.supplyLineGroups?.length > 0"
          >
            <el-option
              v-for="lineTypeItem in Object.entries(MerchantLineTypeEnum)"
              :key="lineTypeItem.at(1)"
              :value="lineTypeItem.at(1)"
              :label="lineTypeItem.at(0)"
            />
          </el-select>
        </el-form-item>
        <br>
        <el-form-item label="线路名称：" prop="lineName">
          <el-input v-model.trim="form.lineName" placeholder="请输入线路名称" clearable style="width: 240px;" />
        </el-form-item>
        <el-form-item label="线路状态：" prop="enableStatus">
          <el-select v-model.trim="form.enableStatus" placeholder="请选择线路状态" style="width: 240px;">
            <el-option
              v-for="lineStatusItem in enum2Options(MerchantLineEnableStatusEnum)"
              :key="lineStatusItem.name"
              :value="lineStatusItem.value"
              :label="lineStatusItem.name"
            />
          </el-select>
        </el-form-item>
        <br>
        <el-form-item label="并发上限：" prop="concurrentLimit">
          <el-input v-model.trim="form.concurrentLimit" placeholder="请输入并发上限" clearable style="width: 240px;" />
        </el-form-item>
        <el-form-item v-loading="loadingIndustry" label="适用行业：" prop="secondIndustry">
          <el-cascader
            v-model="form.secondIndustry"
            :options="industryOptions"
            :props="industryProps"
            placeholder="请选择适用行业"
            clearable
            style="width: 240px;"
            @change="onChangeSecondIndustry"
          />
        </el-form-item>

        <!--运营备注-->
        <div class="form-block-title">
          运营备注
        </div>
        <el-form-item label="备注：">
          <el-input
            v-model.trim="form.notes"
            type="textarea"
            placeholder="请输入备注，不超过250字"
            clearable
            maxlength="250"
            show-word-limit
            autosize
            resize="none"
            style="width: 600px;"
          />
        </el-form-item>
      </div>

      <!--标签卡顶部-->
      <TabsBox
        v-model:active="activeTab"
        :tabList="tabList"
        class="form-block tw-mt-[16px]"
        @update:active="handleTabChange"
      />
      <MerchantLineComposition
        v-if="activeTab===tabList[0]"
        :merchantInfo="form"
        v-model:supplyLineGroups="form.supplyLineGroups"
        :readonly="readonly"
      />

      <!--线路配置-->
      <template v-if="activeTab===tabList[1]">
        <MerchantLineConfig v-if="currentMethod==='编辑线路'" />
        <el-empty v-else description="请保存后再编辑线路配置" />
      </template>
    </el-form>
  </el-scrollbar>

  <!--底部按钮-->
  <div class="submodule-detail">
    <div class="detail-footer">
      <el-button
        :icon="CloseBold"
        :disabled="loadingConfirm"
        @click="handleCancel"
      >
        {{ dialogText.cancelButtonText }}
      </el-button>
      <el-button
        v-if="!merchantStore.readonly"
        type="primary"
        :icon="Select"
        :loading="loadingConfirm"
        :disabled="loadingConfirm"
        @click="handleConfirm"
      >
        {{ dialogText.confirmButtonText }}
      </el-button>
    </div>
  </div>

  <!--地区组重复提示弹窗-->
  <el-dialog
    v-model="dialogScopeMutexVisible"
    align-center
    width="600px"
    :close-on-click-modal="false"
    class="merchant-dialog"
  >
    <template #header>
      <div class="form-dialog-header">
        以下运营商的地区组有重复地区（市），请检查并修改
      </div>
    </template>

    <el-scrollbar class="form-dialog-main" view-class="form-dialog-main-inner">
      <div v-for="item in scopeWrongList">
        <div>{{ item.operator }}</div>
        <div v-for="cityName in item.city" class="tw-pl-[2rem]">
          {{ cityName }}
        </div>
      </div>
    </el-scrollbar>

    <template #footer>
      <div class="form-dialog-footer">
        <el-button :icon="CloseBold" @click="dialogScopeMutexVisible=false">
          关闭
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed, defineAsyncComponent, onMounted, reactive, ref, toRaw, } from 'vue'
import { CloseBold, Select, } from '@element-plus/icons-vue'
import { storeToRefs } from 'pinia'
import { useGlobalStore } from '@/store/globalInfo'
import { ElMessage, FormRules, } from 'element-plus'
import router from '@/router'
import {
  MerchantAccountInfo,
  MerchantInfo,
  MerchantLineConstituteParams,
  MerchantLineInfo,
  MerchantLineInfoParams,
  MerchantLineTypeEnum,
  MerchantLineEnableStatusEnum,
} from '@/type/merchant'
import { merchantModel } from '@/api/merchant'
import { useMerchantStore } from '@/store/merchant'
import { supplierOperatorEnum, supplierOperatorMap } from '@/assets/js/map-supplier'
import { trace } from '@/utils/trace'
import MerchantLineComposition from './Composition.vue'
import { merchantLineInfoDefault } from '../constant'
import { enum2Options } from '@/utils/utils'

// 动态引入组件
const HeaderBox = defineAsyncComponent(() => import('@/components/HeaderBox.vue'))
const TabsBox = defineAsyncComponent(() => import('@/components/TabsBox.vue'))
const MerchantLineConfig = defineAsyncComponent(() => import('./Config.vue'))

// ---------------------------------------- 通用 开始 ----------------------------------------

// 全局
const globalStore = useGlobalStore()
const { allIndustryList, } = storeToRefs(globalStore)
// 商户
const merchantStore = useMerchantStore()

// 当前编辑的线路的相关信息，只读，不应该修改
const account = reactive<MerchantAccountInfo>(merchantStore.currentAccount)
const readonly = ref<boolean>(merchantStore.readonly)
const cityCode2NameMap = ref<Record<string, string>>({})

// 页面标题
const currentMethod = computed(() => form.id === -1 ? '新建线路' : '编辑线路')


onMounted(async () => {
  // 更新省市行政代码与名称映射表
  cityCode2NameMap.value = await globalStore.updateCityCode2NameMap()

  // 更新行业列表
  await updateAllIndustryList()

  // 将线路组成按运营商排序并分类汇总，防止表格不连续
  const tempLine: MerchantLineInfo = JSON.parse(JSON.stringify(merchantStore.editingMerchantLine))
  const tempGroup: MerchantLineConstituteParams[] = tempLine.supplyLineGroups ?? []
  const groupList: {
    [prop: string]: MerchantLineConstituteParams[]
  } = {}
  tempGroup.forEach((item: MerchantLineConstituteParams) => {
    const operator: string = item?.serviceProvider ?? ''
    if (operator) {
      if (groupList[operator]?.length) {
        groupList[operator].push(item)
      } else {
        groupList[operator] = [item]
      }
    }
  })
  const resultGroup: MerchantLineConstituteParams[] = []
  Object.values(groupList).forEach((operatorList: MerchantLineConstituteParams[]) => {
    operatorList.forEach((item: MerchantLineConstituteParams) => {
      resultGroup.push(item)
    })
  })
  tempLine.supplyLineGroups = resultGroup

  readonly.value = merchantStore.readonly
  Object.assign(form, tempLine)

  // 注意接口数据和组件数据的转换
  // 适用行业 虽然是单选 但是接口数据是数组 组件数据是二级行业字符串
  form.secondIndustry = form?.secondIndustries?.at(-1) ?? ''
})

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 表单 开始 ----------------------------------------

// 表单DOM
const formRef = ref()

// 表单内容
const form = reactive<MerchantLineInfo>({ ...merchantLineInfoDefault })
// 正在提交
const loadingConfirm = ref<boolean>(false)
// 表单校验规则
const rules = reactive<FormRules>({
  // 线路名称
  lineName: [{
    required: true, trigger: ['blur', 'change'], validator: (rule: any, value: any, callback: any) => {
      if (value === '') {
        callback(new Error('线路名称不能为空'))
      } else {
        callback()
        if (merchantStore.editingMerchantLine.id === -1) {
          // 创建时，检查是否同名重复
          const hasSameAccount = merchantStore.merchantLineList.some((item) => {
            return item?.lineName === value
          })
          hasSameAccount ? callback(new Error('已存在此线路名称，请勿重复添加')) : callback()
        } else {
          // 编辑时
          callback()
        }
      }
    },
  }],
  // 线路类型
  lineType: [{
    required: true, trigger: ['blur', 'change'], validator: (rule: any, value: any, callback: any) => {
      if (!Object.values(MerchantLineTypeEnum).includes(value)) {
        callback(new Error('线路类型不正确'))
      } else {
        callback()
      }
    }
  }],
  // 线路状态
  enableStatus: [{
    required: true, trigger: ['blur', 'change'], validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        callback(new Error('线路状态不能为空'))
      } else {
        callback()
      }
    },
  }],
  // 并发上限
  concurrentLimit: [{
    required: true, trigger: ['blur', 'change'], validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        callback(new Error('并发上限不能为空'))
      } else {
        if (/^\d+$/.test(value) && value - 0 > 0) {
          callback()
        } else {
          callback(new Error('并发上限格式不正确，请输入正整数'))
        }
      }
    }
  }],
  // 适用行业
  secondIndustry: [{
    required: true, trigger: ['blur', 'change'], validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        callback(new Error('适用行业不能为空'))
      } else {
        callback()
      }
    }
  }],
})
// 弹窗文本
const dialogText = {
  // 编辑实体时的弹窗标题
  editingTitle: '查看供应线路',
  // 新建实体时的弹窗标题
  creatingTitle: '创建供应线路',
  // 取消按钮文本
  cancelButtonText: '取消',
  // 确定按钮文本
  confirmButtonText: '确定',
  // 编辑成功消息
  msgEditSuccessfully: '商户线路编辑成功',
  // 创建成功消息
  msgCreateSuccessfully: '商户线路创建成功',
  // 编辑失败消息
  msgEditFailed: '商户线路编辑失败',
  // 创建失败消息
  msgCreateFailed: '商户线路创建失败',
}

/**
 * 表单提交的具体实现
 */
const submitCallback = async (result: MerchantLineInfo) => {
  // 检查表单，通过后提交创建或编辑好的商户线路

  let statusProblem = false
  let cityProblem = false
  const lineProblem = result.supplyLineGroups?.length && result.supplyLineGroups.some((item: MerchantLineConstituteParams) => {
    if (!item?.cityCodes?.length) {
      ElMessage.warning('地区组为空，请配置地区组')
      return true
    }

    if (!item?.supplyLineNumbers?.length) {
      ElMessage.warning('地区组下缺少线路，请添加线路')
      return true
    }
    // 组成的供应线路存在状态异常，请检查
    
  })

  // 有任何问题，抛出异常，不允许提交
  if (statusProblem || cityProblem || lineProblem) {
    throw new Error()
  }

  // 检查地区组重复
  if (!verifyCityMutex()) {
    throw new Error()
  }

  // 校验通过

  // 将表单数据转换成接口参数
  const {
    id,

    tenantId,
    adminId,

    lineNumber,
    lineType,
    lineName,
    enableStatus,
    concurrentLimit,
    // secondIndustries,

    supplyLineGroups,

    notes,

    groupId
  } = result

  supplyLineGroups?.forEach((item) => {
    item.tenantLineNumber = merchantStore.editingMerchantLine.lineNumber
    // 一些临时变量不需要传
    item.supplyLineList = undefined
    item.supplyLineNameList = undefined
    item.cityCountStrList = undefined
  })

  // 处理参数
  const params: MerchantLineInfoParams = {
    id: id ?? form.id ?? undefined,

    tenantId: tenantId ?? merchantStore.currentMerchant?.id ?? undefined,
    adminId: adminId ?? account.id ?? undefined,
    groupId: groupId ?? account.groupId ?? '',

    lineNumber,
    lineType,
    lineName,
    enableStatus,
    concurrentLimit: parseInt((<string>concurrentLimit)),
    // 注意接口数据和组件数据的转换
    // 适用行业 虽然是单选 但是接口数据是数组 组件数据是二级行业字符串
    secondIndustries: form.secondIndustry ? [form.secondIndustry] : [],

    supplyLineGroups,

    notes,
  }
  trace({ page: `商户管理-${form.id && form.id > -1 ? '编辑' : '创建'}线路`, params })
  // 请求接口
  if (form.id && form.id > -1) {
    // 编辑
    params.tenantLineNumber = form.lineNumber ?? ''
    await merchantModel.editLine(params)
  } else {
    // 新建
    // 没有ID
    params.id = undefined
    params.lineNumber = undefined
    if (params.supplyLineGroups) {
      const list = Array.isArray(supplyLineGroups) ? supplyLineGroups : []
      params.supplyLineGroups = list.map((item: MerchantLineConstituteParams) => {
        item.id = undefined
        return item
      })
    }
    // 剩余可用并发数量和最大并发限制相等，因为还没开始使用这条线路
    params.lineRemainConcurrent = params.concurrentLimit
    await merchantModel.addLine(params)
  }
}
/**
 * 提交
 */
const submit = async () => {
  try {
    // 表单显示成正在提交的加载状态
    loadingConfirm.value = true

    // 请求接口
    await submitCallback(toRaw(form))

    ElMessage({
      message: form.id && form.id > -1 ? dialogText.msgEditSuccessfully : dialogText.msgCreateSuccessfully,
      duration: 3000,
      type: 'success',
    })

    // 关闭弹窗
    closeDialog()
  } catch (e) {
    ElMessage({
      message: form.id && form.id > -1 ? dialogText.msgEditFailed : dialogText.msgCreateFailed,
      duration: 3000,
      type: 'error',
    })
  } finally {
    setTimeout(() => {
      // 取消加载状态
      loadingConfirm.value = false
    }, 200)
  }
}
/**
 * 表单校验
 */
const validForm = () => {
  // 表单DOM不存在
  if (!formRef.value) {
    return
  }
  // 表单DOM
  // 回调函数里的参数valid是指表单校验是否通过，是UI组件传来的
  formRef.value.validate(async (valid: boolean) => {
    // 校验通过
    if (valid) {
      // 发送修改后的数据
      await submit()
    } else {
      // 提示用户检查表单
      ElMessage({
        message: '请按提示正确填写信息',
        duration: 3000,
        type: 'warning',
      })
    }
  })
}
/**
 * 关闭弹窗
 */
const closeDialog = () => {
  // 重置表单
  resetForm()
  // 切换路由
  router.back()
}
/**
 * 重置表单
 */
const resetForm = () => {
  // 清除表单的校验结果
  formRef.value.resetFields()
  // 表单数据恢复默认值
  Object.assign(form, { ...merchantLineInfoDefault })
}
/**
 * 点击取消按钮
 */
const handleCancel = () => {
  // 回到商户列表
  closeDialog()
}
/**
 * 点击确定按钮
 */
const handleConfirm = () => {
  // 表单校验
  validForm()
}

// ---------------------------------------- 表单 结束 ----------------------------------------

// ---------------------------------------- 基本信息 开始 ----------------------------------------

// 适用行业，级联选择器，数据内容
const industryOptions = ref<{
  label: string,
  value: string,
  children: {
    label: string,
    value: string
  }[]
}[]>([])
// 适用行业，级联选择器，配置信息
const industryProps = { multiple: false, emitPath: false }

// 正在加载行业列表
const loadingIndustry = ref<boolean>(false)

/**
 * 更新全部二级行业列表
 */
const updateAllIndustryList = async () => {
  loadingIndustry.value  = true
  await globalStore.getAllIndustryList()
  industryOptions.value = allIndustryList.value.map((primaryItem) => {
    return {
      label: primaryItem?.primaryIndustry ?? '',
      value: primaryItem?.primaryIndustry ?? '',
      children: (primaryItem?.secondaryIndustries ?? []).map((secondItem) => {
        return {
          label: secondItem?.name ?? '',
          value: secondItem?.name ?? '',
        }
      })
    }
  })
  loadingIndustry.value  = false
}
/**
 * 基本信息 更新二级行业 从组件数据更新到接口数据
 */
const onChangeSecondIndustry = () => {
  form.secondIndustries = [form.secondIndustry ?? '']
}

// ---------------------------------------- 基本信息 结束 ----------------------------------------

// ---------------------------------------- 标签卡 开始 ----------------------------------------

let tabList = ['线路组成', '线路配置']
const activeTab = ref('线路组成')

const handleTabChange = (val: string) => {
  switch (val) {
    case '线路组成':
      break;
    case '线路配置':
      break;
  }
}

// ---------------------------------------- 标签卡 结束 ----------------------------------------


// 地区组表格列 同一运营商的地区组是否互斥 只有为true才能允许提交表单到接口
const citesMutexPassed = ref(true)
// 显示地区组重复提示弹窗
const dialogScopeMutexVisible = ref(false)
// 重复地区提示文本内容列表 一层目录运营商 二层目录省市名称
const scopeWrongList = ref<{
  operator: string,
  city: string[]
}[]>([])
/**
 * 地区组表格列 校验同一运营商的地区组是否有城市互斥
 */
const verifyCityMutex = () => {
  // 按运营商分类讨论

  // 先将各个地区组展开放到数组里
  // 然后将数组转换成集合
  // 最后比较两种数据结构的长度
  // 如果集合长度比数组长度小，说明有重复的城市，不符要求，提示用户修改
  // 如果集合长度和数组长度相等，说明城市互斥，符合要求

  // 地区代码 属性名是运营商名
  const arr: {
    [prop: string]: string[]
  } = {}
  // 地区数量 属性名是运营商名
  const arrLengthList: {
    [prop: string]: number
  } = {}
  // 地区代码 去重 属性名是运营商名
  const set: {
    [prop: string]: Set<string>
  } = {}
  // 地区数量 去重 属性名是运营商名
  const setSizeList: {
    [prop: string]: number
  } = {}

  form.supplyLineGroups?.forEach((operatorItem: MerchantLineConstituteParams) => {
    const operator = JSON.parse(JSON.stringify(operatorItem))

    // 运营商名称，接口值
    const operatorName: string = operator?.serviceProvider ?? ''
    // 城市代码
    const codeList: string[] = operator?.cityCodes ?? []

    // 更新数量记录
    if (operatorName) {
      if (arr[operatorName]?.length) {
        arr[operatorName].push(...codeList)
        arrLengthList[operatorName]! += codeList.length
        codeList.forEach((item) => {
          set[operatorName]?.add(item)
        })
        setSizeList[operatorName] = set[operatorName]?.size!
      } else {
        arr[operatorName] = codeList
        arrLengthList[operatorName] = codeList.length
        set[operatorName] = new Set(codeList)
        setSizeList[operatorName] = set[operatorName].size
      }
    }
  })

  let cityCodeList: {
    operator: string,
    city: string[]
  }[] = []

  // 好！数据已经收集完毕，可以判断了
  let hasProblem = false
  Object.keys(arrLengthList).forEach((key) => {
    if (arrLengthList[key]! > setSizeList[key]!) {
      // 将重复的城市筛选出来
      const list: {
        [cityCode: string]: number
      } = {}
      let result: string[] = []
      arr[key]?.forEach((code: string) => {
        list[code] = list[code] ? ++list[code] : 1
        if (list[code] > 1) {
          result.push(code)
        }
      })
      // 代码从低到高排好序存起来，并按省份归类
      result = result.sort((a, b) => a.localeCompare(b))
      result = result.map((code: string) => {
        return cityCode2NameMap.value[code]!
      })
      const item = {
        operator: supplierOperatorMap.get(<supplierOperatorEnum>key)!.text,
        city: result
      }
      cityCodeList.push(JSON.parse(JSON.stringify(item)))
      hasProblem = true
    }
  })

  if (hasProblem) {
    scopeWrongList.value = cityCodeList
    dialogScopeMutexVisible.value = true
    citesMutexPassed.value = false
    return false
  } else {
    citesMutexPassed.value = true
    return true
  }
}

</script>

<style scoped lang="postcss">
.submodule-detail {
  width: 100%;
  min-width: 1080px;
}
</style>
