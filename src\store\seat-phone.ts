import { defineStore, storeToRefs } from 'pinia'
import { h, nextTick, reactive, ref, toRaw, watch } from 'vue'
import { IdleAccount } from '@/type/speech-craft'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import janusUtil, { janusList, sipCallList } from '@/utils/janus'
import {
  ConstructorOptions,
  JSEP,
  OfferParams,
  PluginCreateAnswerParam,
  PluginDtmfParam,
  PluginHandle,
  PluginOptions,
  SipEventMessage,
} from '@/type/janus'
import {
  seatLogModel,
  seatManagerModel,
  seatServerModel,
  seatWorkbenchAccountModel,
  seatWorkbenchCallModel
} from '@/api/seat'
import {
  CallCarryInfo,
  EventInfo,
  EventInfoParams,
  SeatAccountBindInfo,
  SeatCallInfo,
  SeatCallManualRes,
  SeatCallMixInterveneRes,
  SeatCallMixRes,
  SeatCallParam,
  SeatCallRecord,
  SeatCallTypeEnum,
  seatCallTypeList,
  SeatFsCallParam,
  SeatHeartResponse,
  SeatLogActionEnum,
  SeatLogItem,
  SeatLogParam,
  SeatLogTypeEnum,
  SeatMember,
  SeatPageEnum,
  SeatStatusEnum,
  seatStatusList,
  SeatTeam,
  SeatWebSocketMsg
} from '@/type/seat'
import { ClueItem, RelatedTaskItem } from '@/type/clue'
import dayjs from 'dayjs'
import { getBrowserType, logWatchColorfully, removeStrPrefix, Throttle } from '@/utils/utils'
import { Button, notification } from 'ant-design-vue'
import { TaskManageItem } from '@/type/task'
import { aiOutboundTaskModel } from '@/api/ai-report'
import router from '@/router'
import { wsUtil } from '@/utils/websocket'
import { WebSocketConfig } from '@/type/websocket'
import to from 'await-to-js'
import { iceServers, janusServer } from '@/assets/js/constant'
import { MerchantSmsTemplateItem, SmsTemplateParams, SmsTemplateStatusEnum } from '@/type/merchant'
import { merchantSmsTemplateModel } from '@/api/merchant'
import { SmsSendParam, VariableSmsPojoItem } from '@/type/sms'
import { smsSendModel } from '@/api/sms'
import { useUserStore } from '@/store/user'
import { trace } from '@/utils/trace'
import { useCallSettingStore } from '@/store/seat/call-setting'
import { useSeatSettingStore } from '@/store/seat/seat-setting'
import { useSeatInfoStore } from '@/store/seat/seat-info'
import { useInteractionStore } from '@/store/seat/interaction'
import { ResponseData } from "@/axios/request/types";

export const useSeatPhoneStore = defineStore('seatPhone', () => {

  // ---------------------------------------- 通用 开始 ----------------------------------------

  const userStore = useUserStore()
  const callSettingStore = useCallSettingStore()
  const {
    totalTransferSecond,
    leftTransferSecond,
    ringtoneDom
  } = storeToRefs(callSettingStore)
  const seatSettingStore = useSeatSettingStore()
  const { seatSetting } = storeToRefs(seatSettingStore)
  const seatInfoStore = useSeatInfoStore()
  const {
    currentSeat,
    currentSeatTeam,
    seatOnline,
    seatBusy,
    seatStatus,
    seatTaskList,
    seatPage,
  } = storeToRefs(seatInfoStore)
  const interactionStore = useInteractionStore()

  // ---------------------------------------- 通用 结束 ----------------------------------------

  // ---------------------------------------- 线索信息 开始 ----------------------------------------

  // 当前线索
  const currentClue = ref<ClueItem>({})
  // 当前跟进状态的线索列表
  const clueList = ref<ClueItem[]>([])
  // 上条线索
  const lastClue = ref<ClueItem>({})
  // 需要更新线索列表
  const needUpdateClueList = ref<boolean>(false)

  /**
   * 更新当前线索
   * @param {ClueItem} row 线索信息
   */
  const updateCurrentClue = (row: ClueItem) => {
    // 如果不是同一条线索，备份到上条线索
    if (row?.id !== currentClue.value?.id) {
      lastClue.value = JSON.parse(JSON.stringify(toRaw(currentClue.value)))
    }
    // 更新当前线索
    currentClue.value = JSON.parse(JSON.stringify(row))
  }
  /**
   * 清空线索记忆
   */
  const clearClueHistory = () => {
    currentClue.value = {}
    lastClue.value = {}
  }

  // ---------------------------------------- 线索信息 开始 ----------------------------------------

  // ---------------------------------------- 坐席信息 开始 ----------------------------------------

  /**
   * 监听处理，除了坐席工作台，其余页面不打印
   * @param {T | null | undefined} val 新值
   * @param {T | null | undefined} oldVal 旧值
   * @returns {Function} 返回的柯里化形式函数
   */
  const handleWatch = <T>(val: T | null | undefined, oldVal: T | null | undefined): Function => {
    if (router.currentRoute.value.name === 'Workbench') {
      return logWatchColorfully(val, oldVal)
    } else {
      return new Function()
    }
  }

  watch(seatOnline, (val, oldVal) => {
    handleWatch(val, oldVal)('seatOnline', '坐席是否在线', '#13BF77')
  }, { immediate: true })
  watch(seatBusy, (val, oldVal) => {
    handleWatch(val, oldVal)('seatBusy', '坐席是否工作', '#E54B17')
  }, { immediate: true })
  watch(seatStatus, (val, oldVal) => {
    handleWatch(
      val + ' ' + seatStatusList[val].logName,
      oldVal + ' ' + seatStatusList[oldVal ?? SeatStatusEnum.OFF_LINE].logName,
    )('seatStatus', '坐席通话状态', '#165DFF')
    const idleList = [
      SeatStatusEnum.HUMAN_MACHINE_IDLE,
      SeatStatusEnum.MANUAL_DIRECT_IDLE,
    ]
    const busyList = [
      SeatStatusEnum.HUMAN_MACHINE_WINDOW,
      SeatStatusEnum.MANUAL_DIRECT_CALLING,
    ]
    // 坐席从空闲状态切换到通话状态，记录这一次的空闲时长，上报坐席日志
    if (oldVal && idleList.includes(oldVal) && busyList.includes(val)) {
      report({
        type: SeatLogTypeEnum['详细'],
        action: SeatLogActionEnum['坐席空闲时长'],
        desc: '坐席空闲时长：' + idleSecond.value + '秒',
      })
    }
  }, { immediate: true })
  watch(seatTaskList, (val, oldVal) => {
    handleWatch(val, oldVal)('seatTaskList', '坐席签入任务列表', '#E59000')
  }, { immediate: true, deep: true })
  watch(seatPage, (val, oldVal) => {
    handleWatch(
      val + ' ' + SeatPageEnum[val],
      oldVal + ' ' + SeatPageEnum[oldVal ?? SeatPageEnum.CLUE]
    )('seatPage', '当前页面位置', 'pink')
    // 关闭通话抽屉后，重置通话计时器
    if (val === SeatPageEnum.CLUE && oldVal === SeatPageEnum.PHONE) {
      resetReceptionTimer()
    }
  }, { immediate: true })
  watch(currentClue, (val, oldVal) => {
    handleWatch(val, oldVal,)('currentClue', '当前线索信息', 'grey')
  }, { immediate: true, deep: true })

  /**
   * 重置坐席工作台
   * @param {boolean} clearFsInfo 清除FS账号信息
   */
  const resetWorkbench = (clearFsInfo: boolean = true) => {
    seatInfoStore.resetSeatInfo(clearFsInfo)
    clueList.value = []
    clearClueHistory()
    needUpdateClueList.value = false
    iceState.value = 'closed'
    janusErrorTimes = 0
    wsErrorTimes = 0
    clearCallInfo()
  }
  /**
   * 检测坐席上线状态
   */
  const checkOnline = () => {
    if (!seatOnline.value) {
      ElMessage({
        message: '坐席未上线',
        type: 'warning',
      })
      return false
    }
    return true
  }
  /**
   * 检测坐席工作状态
   */
  const checkStatus = () => {
    if (!seatBusy.value || seatStatus.value === SeatStatusEnum.IN_REST) {
      ElMessage({
        message: '坐席休息中',
        type: 'warning',
      })
      return false
    }
    return true
  }
  /**
   * 检测坐席是否签入任务
   */
  const checkTask = () => {
    if (seatTaskList.value.length) {
      ElMessage({
        message: '坐席未签入任务',
        type: 'warning',
      })
      return false
    }
    return true
  }

  // ---------------------------------------- 坐席信息 开始 ----------------------------------------

  // ---------------------------------------- 通话信息 开始 ----------------------------------------

  // 通话信息
  const callInfo = ref<SeatCallInfo>({
    // 人机协同，callId更新时机：显示来电弹窗后
    // 人工直呼，callId更新时机：调用发起呼叫接口后
    callId: '',
    aiCallIp: '',
    aiCallPort: '',
  })

  // 通话类型
  const callType = ref<SeatCallTypeEnum>(SeatCallTypeEnum.DIRECT)

  watch(callType, (val, oldVal) => {
    handleWatch(
      seatCallTypeList[<SeatCallTypeEnum>val]?.name + ' ' + val,
      seatCallTypeList[<SeatCallTypeEnum>oldVal]?.name + ' ' + oldVal
    )('callType', '通话类型', '#722ED1')
  }, { immediate: true })

  // 通话类型配置项
  interface CallTypeConfig {
    // 手动指定类型
    type?: SeatCallTypeEnum
    // 参照的任务列表
    list?: any[]
  }

  /**
   * 更新通话类型
   * @param {SeatCallTypeEnum} config 配置项
   */
  const updateCallType = (config?: CallTypeConfig) => {
    if (config?.type) {
      callType.value = config.type
    } else if (config?.list) {
      callType.value = config?.list?.length ? SeatCallTypeEnum.MONITOR : SeatCallTypeEnum.DIRECT
    } else {
      callType.value = seatTaskList.value.length ? SeatCallTypeEnum.MONITOR : SeatCallTypeEnum.DIRECT
    }
    console.log('***', '更新', '通话类型 callType', seatCallTypeList[<SeatCallTypeEnum>callType.value]?.name + ' ' + callType.value)
  }

  // 表单ID
  const formId = ref<string | null>(null)
  // 跟进记录ID
  const followUpLogId = ref<number | null>(null)
  // 人机协同的话术ID，用于查询AI通话记录和填写人工通话记录等
  const humanMachineSpeechCraftId = ref<number | null>(null)
  // 正在通话的电话号码
  const phone = ref<string | null>(null)
  // 人工直呼通话记录
  const callRecordForManualDirect = ref<SeatCallManualRes>({})
  // 人机协同通话记录
  const callRecordForHumanMachine = ref<SeatCallRecord>({})

  // 人工直呼可以直接读取线索信息
  // 人机协同在弹窗后可以读取接口返回的通话记录数据，但是线索信息只有介入后才能有

  // 人机协同监听时不会生成表单记录、跟进记录等，也没有话后处理，所以没有 followUpLogId

  // formId
  // 人工直呼 呼叫 clue formId
  // 人机协同 开始介入 clue formId

  // followUpLogId 在人工发起通话后生成
  // 人工直呼 呼叫 clueFollowUpLog id
  // 人机协同 开始介入 clueFollowUpLog id

  // callRecordForManualDirect 人工直呼通话记录
  // 人工直呼 来电弹窗时有

  // callRecordForHumanMachine 人机协同通话记录
  // 人机协同 来电弹窗时有

  // phone 正在通话的电话号码
  // 人工直呼 线索里一直有
  // 人机协同 来电弹窗时有

  /**
   * 清除通话信息
   *
   * 人工直呼
   *     呼叫失败
   *         呼叫失败弹窗关闭后
   *     呼叫成功
   *         话后处理提交成功后
   *
   * 人机协同
   *     来电漏接
   *         电话拒接并调用接口后
   *     退出监听
   *         退出监听成功后
   *     接听/介入
   *         话后处理提交成功后（和人工直呼的话后处理提交在一块）
   */
  const clearCallInfo = () => {
    callInfo.value.callId = ''
    callInfo.value.aiCallIp = ''
    callInfo.value.aiCallPort = ''

    formId.value = null

    callRecordForManualDirect.value = {}
    callRecordForHumanMachine.value = {}
    phone.value = ''
    eventList.value = []

    resetCallCarryInfo()

    // 解除一些节流锁
    throttleIntervene.unlock()
    throttleResendSms.unlock()

    // 关闭一些定时器
    interactionStore.stopDelayInterveneTimer()
  }

  // ---------------------------------------- 通话信息 结束 ----------------------------------------

  // ---------------------------------------- Janus和FS通话服务 开始 ----------------------------------------

  // Janus实例ID
  const instanceId = 'workbench'

  // FS账号信息
  const fsAccount = reactive<IdleAccount>({
    account: '',
    password: '',
    ip: '',
    port: '',
    address: '',
  })

  // 接打电话操作，这是一个方法，RTCPeerConnection里的offer或者answer方法
  let sipCallAction: Function | null | undefined = null
  let sipCallActionParams: OfferParams | PluginCreateAnswerParam = {}

  // 对方音频流缓存列表
  let remoteAudioDomList: { [prop: string | number]: HTMLAudioElement } = {}

  /**
   * 切换本机麦克风静音状态
   * @param {boolean} muted 是否静音
   * @returns {boolean} 最终结果
   */
  const switchLocalAudioMuted = (muted: boolean = false): boolean => {
    // 不支持切换静音
    if (!sipCallList[instanceId]?.muteAudio && !sipCallList[instanceId]?.unmuteAudio) {
      ElMessage({
        type: 'warning',
        message: '无法切换麦克风静音状态',
      })
      return false
    }

    if (muted) {
      sipCallList[instanceId]?.muteAudio()
    } else {
      sipCallList[instanceId]?.unmuteAudio()
    }

    const result = sipCallList[instanceId]?.isAudioMuted() ?? true
    const message = '麦克风已' + (result ? '关闭' : '打开')

    ElMessage({
      message: message,
      type: result ? 'warning' : 'success',
      duration: 1000,
    })

    return result ?? false
  }

  // 是否注册
  const registered = ref(false)
  // 是否通话中
  const busy = ref(false)
  // ICE连接状态（TURN服务器连接状态）
  const iceState = ref<RTCIceConnectionState>('closed')

  watch(iceState, (val, oldVal) => {
    handleWatch(val, oldVal,)('iceState', '当前 ICE 状态', 'grey')
    if (report) {
      report({
        type: SeatLogTypeEnum['详细'],
        action: SeatLogActionEnum['音频ICE状态变化'],
        desc: '音频ICE状态变化 ' + val,
      })
    }
  })

  /**
   * 处理Janus SIP事件
   * @param {SipEventMessage} msg 消息内容
   * @param {JSEP} jsepData JSEP
   */
  const handleJanusSipEvent = (msg: SipEventMessage, jsepData: JSEP) => {
    // 根据事件名找到对应的事件处理
    const eventHandler: { [eventName: string]: Function } = {
      registering: () => {
        console.log('***', '账号正在注册')
        registered.value = false
      },
      registration_failed: async () => {
        console.log('***', '账号注册失败')
        ElMessage.error('账号注册失败')
        await requestOffline()
      },
      registered: async () => {
        console.log('***', '账号注册成功', msg?.result?.username)
        registered.value = true
        ElMessage.success(`${currentSeat.value.account} 上线成功`)
        ElMessage.warning('请勿在多设备和多窗口登录该页面，否则将导致通话异常')
        // 创建websocket连接
        wsConfig.url = import.meta.env.VITE_WEBSOCKET_URL + `?fsUser=${fsAccount.account}`
        ws = new wsUtil(wsConfig)
        ws?.connect()
        // 开启心跳定时器
        await startHeartbeatTimer()
      },
      unregistered: async () => {
        console.log('***', '账号已注销', msg?.result?.username)
        await requestOffline()
      },
      incomingcall: async () => {
        console.log('***', '来电', msg?.result, 'JSEP', JSON.parse(JSON.stringify(jsepData)))

        // 根据是否签入任务，判断通话类型
        // 在签入任务变成人机协同空闲后，手动发起人工直呼，此时通话类型也可能是人工直呼来电
        // 保持之前的 callType 不变即可
        // 之前具体是什么时间呢？
        // 如果是人机协同，则之前是指所有时间，都是人机协同没变
        // 如果是人工直呼，则之前是指点击呼叫按钮，进行主动呼叫这个动作开始，通话类型就变成人工直呼了
        // 此时的来电正好继承之前的通话类型，两种一一对应，所以说保持 callType 不变即可
        console.log('***', '来电', '通话类型 callType', seatCallTypeList[<SeatCallTypeEnum>callType.value]?.name + ' ' + callType.value)

        sipCallAction = jsepData?.type === 'offer' ? sipCallList[instanceId]?.createAnswer : sipCallList[instanceId]?.createOffer
        console.log('sipCallAction', 'type', jsepData?.type === 'offer' ? 'answer' : 'offer')
        sipCallActionParams = {
          jsep: JSON.parse(JSON.stringify(jsepData)),
          tracks: [
            { type: 'audio', capture: true, recv: true }
          ],
          success: function (data: JSEP) {
            console.log('***', '接听 sipCallAction 执行成功', 'SDP', data.type)
            report({
              type: SeatLogTypeEnum['详细'],
              action: SeatLogActionEnum['Janus响应'],
              desc: '接听sipCallAction执行成功',
            })
            // 发送接听请求
            sipCallList[instanceId]?.send({
              message: {
                request: 'accept',
                autoaccept_reinvites: false
              },
              jsep: data,
            })
            report({
              type: SeatLogTypeEnum['详细'],
              action: SeatLogActionEnum['Janus请求'],
              desc: 'accept',
            })
          },
          error: function (error: string) {
            console.error('***', '接听 sipCallAction 执行失败', error)
            report({
              type: SeatLogTypeEnum['错误'],
              action: SeatLogActionEnum['Janus响应'],
              desc: '接听sipCallAction执行失败',
            })
            busy.value ? hangup() : decline()
            ElMessageBox.alert(`错误原因：${error}`, '无法接通电话', {
              confirmButtonText: '关闭',
            }).then(() => {
            }).catch(() => {
            }).finally(() => {
            })
          }
        }

        // 通话类型是直呼，并且有线索ID，这通电话就是人工直呼
        // 通话类型是直呼，但是没有线索ID，这通电话应该是人机协同，但是接通会出错，所以拒接
        // 通话类型是监听或介入，这通电话就是人机协同

        // 如果是人工直呼，来电时自动接听，并且关闭呼叫弹窗
        // 如果是监听和介入，来电时手动接听

        if (callType.value === SeatCallTypeEnum.DIRECT) {
          // 人工直呼来电
          // 自动接听，关闭呼叫弹窗
          report({
            action: SeatLogActionEnum['人工直呼-来电'],
            desc: '人工直呼 来电',
            phone: currentClue.value.phone,
          })
          // 隐藏呼叫弹窗
          hideCalling()
          if (sipCallAction) {
            sipCallAction(sipCallActionParams)
            // 打开通话界面
            seatInfoStore.updateSeatPage(SeatPageEnum.PHONE)
          }
        } else {
          // 人机协同来电
          // 清空通话信息
          clearCallInfo()
          // 调用接口
          const [err, res] = <[any, SeatCallMixRes]>await to(seatWorkbenchCallModel.incomingHumanMachine())
          // 返回失败结果
          if (err) {
            console.error('***', '来电 接口调用失败', err)
            report({
              type: SeatLogTypeEnum['错误'],
              action: SeatLogActionEnum['人机协同-来电'],
              desc: '人机协同 来电 接口调用失败：' + err.toString(),
              response: err,
            })
            // 提示错误，关闭抽屉，回到线索列表，坐席状态更新为空闲中
            ElMessageBox.alert('点击关闭按钮回到工作台', '获取电话信息时出错', {
              confirmButtonText: '关闭',
            }).then(() => {
            }).catch(() => {
            }).finally(() => {
              // 拒接
              decline()
              // 隐藏新来电提示
              hideIncomingCall()
              // 坐席状态更新为空闲中
              seatInfoStore.updateSeatStatusByTaskList()
            })
            return
          }
          // 返回成功结果
          // 显示新来电提示
          showIncomingCall()
          // 更新坐席状态
          seatInfoStore.updateSeatStatus(SeatStatusEnum.HUMAN_MACHINE_WINDOW)
          // 更新通话信息
          callInfo.value.callId = res.callId || ''
          callInfo.value.aiCallIp = res.aiCallIp || ''
          callInfo.value.aiCallPort = res.aiCallPort || ''
          res.speechCraftId && (humanMachineSpeechCraftId.value = res.speechCraftId ?? null)
          callRecordForHumanMachine.value = res.callRecordForHumanMachine ?? {}
          phone.value = res.callRecordForHumanMachine?.recordId ?? null
          getCallCarryInfo().then(() => {
          }).catch(() => {
          })
          // 更新通话类型
          updateCallType({
            type: <SeatCallTypeEnum>(res?.callTeamHandleType ?? SeatCallTypeEnum.MONITOR)
          })
          report({
            action: SeatLogActionEnum['人机协同-来电'],
            desc: '人机协同 来电',
            phone: callRecordForHumanMachine.value.phone,
          })
          // 坐席绑定电话号码，并初始化客户状态记录
          await bindSeatAndPhone(callRecordForHumanMachine.value?.phone)
          // 如果开启了人机协同自动监听/接听
          // 那就在这里执行，自动帮坐席点击监听/接听按钮
          if (seatSetting.value.autoAccept) {
            await handleAccept(true)
          }
        }
      },
      accepted: async () => {
        console.log('***', '接听', '通话类型 callType', callType.value)

        busy.value = true

        // 打开通话界面
        seatInfoStore.updateSeatPage(SeatPageEnum.PHONE)
        // 通知接口电话呼通
        try {
          if (callType.value === SeatCallTypeEnum.DIRECT) {
            console.log('人工直呼 接听')
            ElMessage({
              type: 'success',
              message: `电话已接通`,
              duration: 1000,
            })
            // 播放接通提示音
            playAcceptAudio()
            if (sipCallAction) {
              try {
                sipCallAction(sipCallActionParams)
              } catch (e) {
              }
            }
            // 调用接口
            const params = {
              clueId: currentClue.value.id,
            }
            const [err, _] = <[any, any]>await to(seatWorkbenchCallModel.startManualDirectCall(params))
            // 返回失败结果
            if (err) {
              console.error('***', '人工直呼 接听 接口调用失败', err)
              report({
                type: SeatLogTypeEnum['错误'],
                action: SeatLogActionEnum['人工直呼-接听'],
                desc: '人工直呼 接听 接口调用失败' + err.toString(),
                params,
                response: err,
              })
              // 提示错误，关闭抽屉，回到线索列表，坐席状态更新为空闲中
              ElMessageBox.alert('点击关闭按钮回到工作台', '接听电话时发生错误', {
                confirmButtonText: '关闭',
              }).then(() => {
              }).catch(() => {
              }).finally(() => {
                // 挂断
                hangup()
                // 处理呼叫失败
                handleCallFail(currentClue.value)
                // 坐席状态更新为空闲中
                seatInfoStore.updateSeatStatusByTaskList()
              })
              return
            }
            // 返回成功结果
            // 开始接待计时
            openReceptionTimer()
            // 更新坐席状态
            seatInfoStore.updateSeatStatus(SeatStatusEnum.MANUAL_DIRECT_DIALING)
            report({
              action: SeatLogActionEnum['人工直呼-接听'],
              desc: '人工直呼 接听',
              phone: currentClue.value.phone,
              params,
            })
            // 更新通话信息
            phone.value = currentClue.value.clueUniqueId ?? null
            // 绑定客户电话，更新客户状态记录
            await bindSeatAndPhone(currentClue.value.phone)
            // 如果开启了自动重发备注短信，判断短信模板签名并决定是否自动发送
            if (seatSetting.value.autoResendSms) {
              const sms: string = (currentClue.value.comment ?? '').trim()
              if (sms.startsWith('【')) {
                await resendSms(true)
              }
            }
          } else if (callType.value === SeatCallTypeEnum.ANSWER) {
            console.log('人机协同 接管')
            // 接管需要等待ICE连接成功后再发送DTMF
            await handleTakeover()
          }
        } catch (e) {
        }
      },
      progress: () => {
        console.log('***', '处理早期媒体')
        // 早期媒体，电话拨打中但还没接通时
        sipCallList[instanceId]?.handleRemoteJsep({
          jsep: JSON.parse(JSON.stringify(jsepData)),
          success: function (data: JSEP) {
            console.log('***', '处理早期媒体成功', data)
            busy.value = true
          },
          error: function (error: string) {
            console.error('***', '处理早期媒体失败', error)
            busy.value = false
            ElMessage({
              message: '处理早期媒体时出错',
              type: 'error'
            })
          }
        })
      },
      declining: async () => {
        console.log('***', '正在拒接')
      },
      hangup: async () => {
        console.log('***', '挂断电话')
        // 停止接待计时
        closeReceptionTimer()
        // 播放挂断提示音
        playHangupAudio()
        ElMessage({
          message: busy.value ? '电话已挂断' : '电话已漏接',
          type: 'warning',
          duration: 1000,
        })
        // 音频连接状态标记为关闭
        iceState.value = 'closed'
        // 通话状态标记为挂断
        busy.value = false

        // 按通话类型区分（人工直呼、人机协同监听，人机协同接管）
        try {
          if (callType.value === SeatCallTypeEnum.DIRECT) {
            // 人工直呼
            // 调用接口
            const params = {
              clueId: currentClue.value.id,
            }
            const [err, _] = await to(seatWorkbenchCallModel.stopManualDirectCall(params))
            if (err) {
              // 返回失败结果
              console.error('***', '人工直呼 挂断 接口调用失败', err)
              report({
                type: SeatLogTypeEnum['错误'],
                action: SeatLogActionEnum['人工直呼-挂断'],
                desc: '人工直呼 挂断 接口调用失败' + err.toString(),
                params,
                response: err,
              })
              ElMessage.error('挂断电话时发生错误')
            } else {
              // 返回成功结果
              report({
                action: SeatLogActionEnum['人工直呼-挂断'],
                desc: '人工直呼 通话中挂断',
                phone: currentClue.value.phone,
              })
            }
            // 更新坐席状态
            seatInfoStore.updateSeatStatus(SeatStatusEnum.MANUAL_DIRECT_POSTING)
          } else if (callType.value === SeatCallTypeEnum.MONITOR) {
            // 人机协同监听

            // 按坐席状态区分情况
            if (seatStatus.value === SeatStatusEnum.HUMAN_MACHINE_WINDOW) {
              // 来电弹窗时挂断，算作漏接
              report({
                action: SeatLogActionEnum['人机协同-挂断'],
                desc: '人机协同 来电漏接',
                phone: callRecordForHumanMachine.value.phone,
              })
              // 本地更新坐席状态为空闲中
              seatInfoStore.updateSeatStatusByTaskList()
              // 接口更新坐席状态为空闲中
              const acceptTimeoutParams: SeatFsCallParam = {
                seatId: currentSeat.value.id,
                callId: callInfo.value.callId,
                aiCallIp: callInfo.value.aiCallIp,
                aiCallPort: callInfo.value.aiCallPort,
              }
              const [acceptTimeoutError, _] = <[any, any]>await to(seatServerModel.acceptTimeout(acceptTimeoutParams))
              report({
                type: SeatLogTypeEnum[acceptTimeoutError ? '错误' : '详细'],
                action: SeatLogActionEnum['通知FS'],
                desc: '通知FS接口seat-miss-call' + (acceptTimeoutError ? '发生错误' : ''),
                phone: callRecordForHumanMachine.value.phone,
                params: acceptTimeoutParams || {},
              })
              trace({
                page: '通知FS接口seat-miss-call' + (acceptTimeoutError ? '发生错误' : ''),
                params: acceptTimeoutParams,
              })
              await seatWorkbenchCallModel.failHumanMachine({
                recordId: callRecordForHumanMachine.value?.recordId,
                isMiss: true,
              })
              // 清空通话信息
              clearCallInfo()
            } else if (seatStatus.value === SeatStatusEnum.HUMAN_MACHINE_LISTEN) {
              // 监听时挂断
              report({
                action: SeatLogActionEnum['人机协同-挂断'],
                desc: '人机协同 监听时挂断',
                phone: callRecordForHumanMachine.value.phone,
              })
              // 显示退出监听弹窗
              dialogExitMonitorVisible.value = true
              // 这里不要更新坐席状态，等退出监听弹窗关闭后再更新到空闲中
            } else if (seatStatus.value === SeatStatusEnum.HUMAN_MACHINE_DIALING) {
              // 介入时挂断
              report({
                action: SeatLogActionEnum['人机协同-挂断'],
                desc: '人机协同 介入时挂断',
                phone: callRecordForHumanMachine.value.phone,
              })
              // 本地更新坐席状态为话后处理中
              seatInfoStore.updateSeatStatus(SeatStatusEnum.HUMAN_MACHINE_POSTING)
              // 接口更新坐席状态为话后处理中
              await seatWorkbenchCallModel.stopSpeakHumanMachine({
                recordId: callRecordForHumanMachine.value?.recordId,
                endAnswerTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
              })
            } else {
              // 其余情况为不正常的情况，直接回到空闲中
              seatInfoStore.updateSeatStatusByTaskList()
              // 清空通话信息
              clearCallInfo()
            }

          } else if (callType.value === SeatCallTypeEnum.ANSWER) {
            // 人机协同接管

            // 按坐席状态区分情况
            if (seatStatus.value === SeatStatusEnum.HUMAN_MACHINE_WINDOW) {
              // 来电弹窗时挂断，算作漏接
              report({
                action: SeatLogActionEnum['人机协同-挂断'],
                desc: '人机协同 来电漏接',
                phone: callRecordForHumanMachine.value.phone,
              })
              // 本地更新坐席状态为空闲中
              seatInfoStore.updateSeatStatusByTaskList()
              // 接口更新坐席状态为空闲中
              const acceptTimeoutParams: SeatFsCallParam = {
                seatId: currentSeat.value.id,
                callId: callInfo.value.callId,
                aiCallIp: callInfo.value.aiCallIp,
                aiCallPort: callInfo.value.aiCallPort,
              }
              const [acceptTimeoutError, _] = <[any, any]>await to(seatServerModel.acceptTimeout(acceptTimeoutParams))
              report({
                type: SeatLogTypeEnum[acceptTimeoutError ? '错误' : '详细'],
                action: SeatLogActionEnum['通知FS'],
                desc: '通知FS接口seat-miss-call' + (acceptTimeoutError ? '发生错误' : ''),
                phone: callRecordForHumanMachine.value.phone,
                params: acceptTimeoutParams || {},
              })
              trace({
                page: '通知FS接口seat-miss-call' + (acceptTimeoutError ? '发生错误' : ''),
                params: acceptTimeoutParams,
              })
              await seatWorkbenchCallModel.failHumanMachine({
                recordId: callRecordForHumanMachine.value?.recordId,
                isMiss: true,
              })
              // 清空通话信息
              clearCallInfo()
            } else if (seatStatus.value === SeatStatusEnum.HUMAN_MACHINE_DIALING) {
              // 接管时挂断
              report({
                action: SeatLogActionEnum['人机协同-挂断'],
                desc: '人机协同 接管时挂断',
                phone: callRecordForHumanMachine.value.phone,
              })
              // 本地更新坐席状态为话后处理中
              seatInfoStore.updateSeatStatus(SeatStatusEnum.HUMAN_MACHINE_POSTING)
              // 接口更新坐席状态为话后处理中
              await seatWorkbenchCallModel.stopSpeakHumanMachine({
                recordId: callRecordForHumanMachine.value?.recordId,
                endAnswerTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
              })
            } else {
              // 其余情况为不正常的情况，直接回到空闲中
              seatInfoStore.updateSeatStatusByTaskList()
              // 清空通话信息
              clearCallInfo()
            }
          }
        } catch (e) {
        }
      },
    }

    report({
      type: SeatLogTypeEnum['详细'],
      action: SeatLogActionEnum['Janus响应'],
      desc: msg?.result?.event ?? '',
    })

    return (eventName: string) => {
      eventHandler[eventName] && eventHandler[eventName]()
    }
  }

  /**
   * 初始化Janus
   * @param {Function} callback 回调函数
   */
  const createJanus = (callback?: Function) => {
    // 如果已经有实例，就不需要重复创建了
    if (janusList[instanceId] && sipCallList[instanceId]) {
      return
    }

    console.log('iceServers', JSON.parse(JSON.stringify(iceServers)))

    // 初始化Janus并创建实例对象
    janusUtil.init(instanceId, <ConstructorOptions>{
      server: janusServer,
      iceServers,
      success: function () {
        // 重试错误尝试次数
        janusErrorTimes = 0

        // 创建成功后，加载SIP插件
        janusUtil.attach(instanceId, <PluginOptions>{
          plugin: 'janus.plugin.sip',
          opaqueId: 'sip-' + janusUtil.randomString(12),
          success: function (handle: PluginHandle) {
            // SIP插件加载完成
            sipCallList[instanceId] = handle
            if (callback) {
              callback()
            }
          },
          iceState: function (state: RTCIceConnectionState) {
            iceState.value = state
          },
          slowLink: function (uplink: boolean, lost: number, mid: string) {
            console.log('***', '连接问题', '数据流方向', uplink, '丢包', lost, '媒体ID', mid)
            ElMessage.warning('Janus SIP 通话服务发生网络波动，请注意网络环境')
          },
          onmessage: async function (msg: SipEventMessage, jsepData: JSEP) {
            // 事件名
            const event: string = msg?.result?.event ?? ''
            console.log('###', 'Janus 事件', event)

            // 已注册
            if (msg?.error_code === 445) {
              handleJanusSipEvent(msg, jsepData)('registered')
            }

            // 处理事件
            handleJanusSipEvent(msg, jsepData)(event)
          },
          onlocaltrack: function (track: MediaStreamTrack, on: boolean) {
            console.log('***', '本地媒体流是否激活', on, '本地媒体流是否静音', track.muted, '本地媒体流', track)
          },
          onremotetrack: function (track: MediaStreamTrack, mid: string, on: boolean) {
            console.log('***', '远程媒体流是否激活', on, '远程媒体流是否静音', track.muted, '远程媒体流', track)
            // 如果媒体流是音频、没有静音、激活状态
            if (track?.kind === 'audio' && !track.muted && on) {
              // 将音频流添加到本地缓存列表
              remoteAudioDomList[mid] = new Audio()
              remoteAudioDomList[mid].srcObject = new MediaStream([track])

              // 播放音频，播放前需要音频元素重新加载源
              // 音频播放是异步的，所以需要Promise
              remoteAudioDomList[mid].load()
              remoteAudioDomList[mid].play().then(() => {
                console.log('***', '开始播放对方音频流', '媒体ID', mid, '对方音频流缓存列表 remoteAudioDomList', remoteAudioDomList)
              }).catch((err) => {
                console.error('***', '无法播放对方音频流', err)
              })
            }
          },
          oncleanup: function () {
            // 停止播放音频
            Object.values(remoteAudioDomList).forEach((audio) => {
              if (audio) {
                audio.pause()
                audio.srcObject = null
                audio.load()
                console.info('***', '停止播放对方音频流')
              }
            })
          },
        })
      },
      error: async function () {
        ElMessage.error('Janus 断开连接，请检查网络')
        // 尝试重连
        await handleJanusError()
      },
    })
  }
  /**
   * 注册账号
   */
  const register = () => {
    sipCallList[instanceId]?.send({
      message: {
        // 事件名，注册FS账号，必填
        request: 'register',
        // 强制使用TCP，选填，默认false不强制
        force_tcp: true,
        // SIP服务器地址，格式 sip:**************[:5060]，必填
        proxy: `sip:${fsAccount.address}`,
        // SIP账号，格式 sip:1016@**************[:5060]，必填
        username: `sip:${fsAccount.account}@${fsAccount.address}`,
        // SIP用户名，如果指定此字段，则覆盖上面的SIP账号字段，选填，默认空字符串
        authuser: fsAccount.account,
        // SIP密码，必填
        secret: fsAccount.password,
        // 展示的昵称，选填，默认空字符串
        display_name: fsAccount.account,
      }
    })
    console.log('@@@', '注册账号信息', JSON.parse(JSON.stringify(fsAccount)))
    report({
      type: SeatLogTypeEnum['详细'],
      action: SeatLogActionEnum['Janus请求'],
      desc: 'register',
    })
  }
  /**
   * 销毁Janus实例
   */
  const destroyJanus = () => {
    busy.value = false
    registered.value = false
    janusUtil.destroy(instanceId)
  }

  /**
   * 获取当前坐席的账号信息
   */
  const updateSeatInfo = async () => {
    try {
      const res = <SeatMember>await seatWorkbenchAccountModel.getSeatInfo()
      seatInfoStore.updateCurrentSeat(res)

      // 通话类型
      updateCallType({
        list: res.taskIds
      })

      // 更新坐席在线状态和工作状态
      if (res?.callSeatStatus === SeatStatusEnum.OFF_LINE) {
        // 离线

        // 是否在线，否
        seatInfoStore.updateSeatOnline(false)
        // 工作休息状态，工作中
        seatInfoStore.updateSeatBusy(true)
        // 通话状态，离线
        seatInfoStore.updateSeatStatus(SeatStatusEnum.OFF_LINE)
        // 任务列表为空
        seatInfoStore.updateSeatTaskList([])
      } else {
        // 在线

        // 是否在线，是
        seatInfoStore.updateSeatOnline(true)
        // 工作休息状态，按接口给的来确定，默认工作中
        seatInfoStore.updateSeatBusy(res.callSeatStatus !== SeatStatusEnum.IN_REST)
        // 通话状态，按接口给的来确定，默认人工直呼空闲中
        seatInfoStore.updateSeatStatus(res.callSeatStatus || SeatStatusEnum.MANUAL_DIRECT_IDLE)

        // 如果有签入任务，还需要更新任务列表，并重新更新坐席状态
        if (res.taskIds?.length) {
          // 如果坐席签入任务不为空，并且处于人工直呼空闲中，则切换到人工直呼空闲中
          if (seatStatus.value === SeatStatusEnum.MANUAL_DIRECT_IDLE) {
            seatInfoStore.updateSeatStatus(SeatStatusEnum.HUMAN_MACHINE_IDLE)
          }
          // 由ID获取任务信息
          try {
            const taskRes = <TaskManageItem[]>await aiOutboundTaskModel.getTaskListByTaskIds(res.taskIds)
            seatInfoStore.updateSeatTaskList(Array.isArray(taskRes) ? taskRes : [])
          } catch (e) {
            ElMessage.warning('无法获取当前坐席已签入的任务信息')
          }
        } else {
          // 如果坐席签入任务为空，并且处于人机协同空闲中，则切换到人工直呼空闲中
          if (seatStatus.value === SeatStatusEnum.HUMAN_MACHINE_IDLE) {
            seatInfoStore.updateSeatStatus(SeatStatusEnum.MANUAL_DIRECT_IDLE)
          }
          seatInfoStore.updateSeatTaskList([])
        }
      }

      console.log('坐席信息', JSON.parse(JSON.stringify(currentSeat.value)), '坐席状态', res?.callSeatStatus)

      // 更新坐席所在坐席组的信息
      if (!currentSeat.value.callTeamId) {
        throw new Error('无法获取坐席组信息，坐席组ID为空')
      } else {
        const [seatTeamErr, seatTeamRes] = <[any, SeatTeam]>await to(seatManagerModel.getSeatTeam({
          callTeamId: currentSeat.value.callTeamId,
        }))
        if (seatTeamErr) {
          throw new Error('无法获取坐席组信息，接口错误：' + seatTeamErr.toString())
        } else if (seatTeamRes) {
          seatInfoStore.updateCurrentSeatTeam(seatTeamRes)
          console.log('坐席组信息', JSON.parse(JSON.stringify(currentSeatTeam.value)))
        }
      }

    } catch (e: any) {
      seatInfoStore.updateCurrentSeat({})
      ElMessage({
        message: '无法获取当前坐席的账号信息：' + e.msg,
        type: 'error',
      })
    }
  }

  /**
   * 介入
   * 先发送DTMF，再请求接口
   */
  const intervene = async () => {
    if (iceState.value !== 'connected' && iceState.value !== 'completed') {
      ElMessage.warning('正在连接通话音频，请稍后重试')
      report({
        type: SeatLogTypeEnum['警告'],
        action: SeatLogActionEnum['人机协同-介入'],
        desc: '正在连接通话音频 音频ICE状态 ' + iceState.value,
        phone: callRecordForHumanMachine.value.phone,
      })
      trace({
        page: '介入 正在连接通话音频 音频ICE状态 ' + iceState.value,
      })
      return
    }

    // 节流锁上锁
    if (throttleIntervene.check()) {
      ElMessage.warning('正在尝试介入，请稍候')
      return
    }
    throttleIntervene.lock()

    // 请求接口的业务逻辑封装成内部方法，以便DTMF发送成功后再调用
    const apiCallback = async () => {
      const interveneParams: SeatCallParam = {
        recordId: callRecordForHumanMachine.value?.recordId,
        isTransToHuman: false,
      }
      const [err, res] = <[any, SeatCallMixInterveneRes]>await to(seatWorkbenchCallModel.startSpeakHumanMachine(interveneParams))

      // 节流锁解锁
      throttleIntervene.unlock()

      // 返回失败结果
      if (err) {
        ElMessage.error('介入失败')
        const errMsg = (err as ResponseData).msg || (err as Error).message || err as string || ''
        report({
          type: SeatLogTypeEnum['错误'],
          action: SeatLogActionEnum['人机协同-介入'],
          desc: '人机协同 介入失败 接口错误：' + errMsg,
          phone: callRecordForHumanMachine.value.phone,
          params: interveneParams || {},
          response: err || {},
        })
        if (errMsg?.includes('挂断')) {
          // 客户已经挂断无法介入
          report({
            action: SeatLogActionEnum['人机协同-挂断'],
            desc: '人机协同 介入失败 接口错误：客户已经挂断无法介入',
            phone: callRecordForHumanMachine.value.phone,
          })
          // 显示退出监听弹窗
          dialogExitMonitorVisible.value = true
        }
        hangup()
        return
      }

      // 返回成功结果
      ElMessage.success('介入成功')
      report({
        type: SeatLogTypeEnum['信息'],
        action: SeatLogActionEnum['人机协同-介入'],
        desc: '人机协同 介入 请求接口',
        phone: callRecordForHumanMachine.value.phone,
        params: interveneParams || {},
        response: res || {},
      })
      // 开始接待计时
      openReceptionTimer()
      // 更新线索
      updateCurrentClue({
        id: res.clueId ?? -1,
        callSeatId: res.callSeatId ?? -1,
      })
      console.log('人机协同 介入 更新线索信息', JSON.parse(JSON.stringify(currentClue.value)))
      // 更新表单ID
      formId.value = res?.formId ?? null
      // 更新坐席状态
      seatInfoStore.updateSeatStatus(SeatStatusEnum.HUMAN_MACHINE_DIALING)
    }

    // 发送DTMF
    sipCallList[instanceId]?.dtmf(<PluginDtmfParam>{
      // 和FS约定双星号**表示坐席介入
      dtmf: { tones: '**' },
      success: (data: any) => {
        console.log('DTMF success', data)
        console.log('***', '发送动作请求', 'DTMF', '**')
        report({
          type: SeatLogTypeEnum['信息'],
          action: SeatLogActionEnum['人机协同-介入'],
          desc: '人机协同 介入 发送DTMF成功',
          phone: callRecordForHumanMachine.value.phone,
        })

        // 发送成功，调用接口
        apiCallback()
      },
      error: (error: string) => {
        console.error('DTMF error', error)
        ElMessage.error('无法发送“介入”请求')
        report({
          type: SeatLogTypeEnum['错误'],
          action: SeatLogActionEnum['人机协同-介入'],
          desc: '人机协同 介入失败 发送DTMF失败',
          phone: callRecordForHumanMachine.value.phone,
          response: error || {},
        })

        // 节流锁解锁
        throttleIntervene.unlock()
      }
    })
  }
  /**
   * 挂断
   */
  const hangup = async () => {
    // 清理接管状态
    cleanupTakeover()

    // 如果在线并且有通话进行
    sipCallList[instanceId]?.send({
      message: {
        request: 'hangup',
        autoaccept_reinvites: false
      },
    })
    console.log('***', '发送动作请求', '挂断')
    report({
      type: SeatLogTypeEnum['详细'],
      action: SeatLogActionEnum['Janus请求'],
      desc: 'hangup',
    })
  }
  /**
   * 取消呼叫
   */
  const cancelCall = async () => {
    // 关闭弹窗
    hideCalling()
    ElMessage.warning('已取消呼叫')
    // 主动取消呼叫
    const msg = {
      fsIp: fsAccount.ip,
      fsUser: fsAccount.account,
      callId: callInfo.value.callId,
      action: 'hangup',
    }
    ws?.send(msg)
    report({
      type: SeatLogTypeEnum['详细'],
      action: SeatLogActionEnum['WebSocket请求'],
      desc: '人工直呼 取消呼叫',
      params: msg || {},
    })
    report({
      type: SeatLogTypeEnum['信息'],
      action: SeatLogActionEnum['人工直呼-取消呼叫'],
      desc: '人工直呼 取消呼叫',
    })
    // 更新坐席状态和通话类型
    seatInfoStore.updateSeatStatusByTaskList()
    updateCallType()
  }
  /**
   * 拒接
   */
  const decline = () => {
    sipCallList[instanceId]?.send({
      message: {
        request: 'decline',
      },
    })
    console.log('***', '发送动作请求', '拒绝接听')
    report({
      type: SeatLogTypeEnum['详细'],
      action: SeatLogActionEnum['Janus请求'],
      desc: 'decline',
    })
    report({
      action: SeatLogActionEnum['人机协同-拒接'],
      desc: '拒接',
    })
  }

  // ---------------------------------------- Janus和FS通话服务 结束 ----------------------------------------

  // ---------------------------------------- 心跳检测 开始 ----------------------------------------

  // 心跳检测定时器
  let heartTimer: any = null
  // 自动签出任务通知 DOM挂载节点
  const checkOutTaskNotificationRef = ref()
  // 关联任务启动通知 DOM挂载节点
  const linkedTaskStartNotificationRef = ref()
  // 关联任务启动通知 通知队列
  let notificationKeyList: string[] = []
  // 是否需要更新坐席工作台统计数据
  const needUpdateWorkbenchStatistics = ref<boolean>(false)

  /**
   * 启动心跳检测定时器
   */
  const startHeartbeatTimer = async () => {
    if (heartTimer) {
      return
    }
    await handleHeart()
    heartTimer = setInterval(async () => {
      await handleHeart()
    }, 60000)
  }
  /**
   * 关闭心跳检测定时器
   */
  const stopHeartbeatTimer = () => {
    if (!heartTimer) {
      return
    }
    clearInterval(heartTimer)
    heartTimer = null
  }
  /**
   * 发送心跳包并处理响应结果
   */
  const handleHeart = async () => {
    try {
      const res = <SeatHeartResponse>await seatServerModel.heart({
        seatId: currentSeat.value.id ?? -1,
      })

      // 更新坐席工作台的统计数据
      needUpdateWorkbenchStatistics.value = true

      // 响应结果里有四种任务通知，如果有就弹窗提示任务停止，如果没有就忽略
      // 任务已停止
      if (res?.stoppedTasks?.length) {
        await autoCheckoutTask(res.stoppedTasks)
      }
      // 被移出任务
      if (res?.movedOutTasks?.length) {
        await autoCheckoutTask(res.movedOutTasks)
      }
      // 任务不在拨打时段
      if (res?.outWorkTimeTasks?.length) {
        await autoCheckoutTask(res.outWorkTimeTasks)
      }
      // 关联任务已启动
      if (res?.startedNotCheckInTasks?.length) {
        notifyTaskStart(res?.startedNotCheckInTasks ?? [])
      }
    } catch (e) {
    }
  }
  /**
   * 自动签出任务
   * @param {RelatedTaskItem[]} list 需要签出的任务列表
   */
  const autoCheckoutTask = async (list: RelatedTaskItem[]) => {
    const checkedTaskIdList = seatTaskList.value.map((task: RelatedTaskItem) => {
      return task.id ?? -1
    }) || []
    const checkedTaskIdSet = new Set(checkedTaskIdList)
    checkedTaskIdSet.delete(-1)

    // 需要签出任务ID列表
    const needCheckOutTaskIdList = list.map((task: RelatedTaskItem) => {
      return task.id ?? -1
    }) || []
    const needCheckOutTaskIdSet = new Set(needCheckOutTaskIdList)
    needCheckOutTaskIdSet.delete(-1)

    // 筛选出不用签出的任务ID
    const reserveSet = new Set(Array.from(checkedTaskIdSet) || [])
    Array.from(reserveSet.values()).forEach((id: number) => {
      // 需要签出
      if (needCheckOutTaskIdSet.has(id)) {
        reserveSet.delete(id)
      }
    })
    const reserveTaskIdList: number[] = Array.from(reserveSet) || []

    // 剩余签入任务列表
    const reserveTaskList = seatTaskList.value.filter((task: RelatedTaskItem) => {
      return !!reserveTaskIdList.find((taskId: number) => {
        return taskId === task.id
      })
    })

    // 更新签入任务列表
    seatInfoStore.updateSeatTaskList(JSON.parse(JSON.stringify(reserveTaskList)))

    // 空闲中就根据剩余签入任务数判断通话类型，不空闲就保持现状不改动
    if (seatStatus.value === SeatStatusEnum.MANUAL_DIRECT_IDLE || seatStatus.value === SeatStatusEnum.HUMAN_MACHINE_IDLE) {
      updateCallType()
      seatInfoStore.updateSeatStatusByTaskList()
    }

    // 这里的自动签出任务是前端页面的更新
    // 后端接口已经更新过了，所以不需要再请求签出

    report({
      action: SeatLogActionEnum['自动签出任务'],
      desc: '自动签出任务',
    })

    // 更新线索统计信息
    needUpdateWorkbenchStatistics.value = true

    // 如果剩余任务列表从有到无，提示任务已全部停止
    if (checkedTaskIdList.length && !reserveTaskList.length) {
      ElNotification({
        title: '签入任务已全部停止',
        message: '请签入其他任务或联系任务管理员开启新任务',
        type: 'warning',
        duration: 5000,
        offset: 40,
        customClass: 'auto-check-out-task-notification',
        appendTo: checkOutTaskNotificationRef.value,
      })
    }
  }
  /**
   * 通知提示 自动签出任务
   * @param {string} title 通知标题文本
   */
  const notifyCheckOut = (title: string = '') => {
    ElNotification({
      title,
      message: '将自动签出该任务',
      type: 'warning',
      duration: 5000,
      offset: 40,
      customClass: 'auto-check-out-task-notification',
      appendTo: checkOutTaskNotificationRef.value,
    })
  }
  /**
   * 通知提示 关联任务已启动
   * @param {TaskManageItem[]} taskList 需要签入的任务列表
   */
  const notifyTaskStart = (taskList: TaskManageItem[] = []) => {
    const key = `notify-task-start-${Date.now()}`
    const str = taskList.length > 1
      ? `【${taskList[0]!.taskName ?? ''}】等${taskList.length ?? '多'}个关联的任务已启动，是否签入该任务？`
      : `关联的任务【${taskList.at(0)?.taskName ?? ''}】已启动，是否签入该任务？`
    notificationKeyList.push(key)

    /**
     * 内部函数方法，签入任务
     */
    const handleCheckInTask = async (actionType?: string) => {
      // if (seatStatus.value !== SeatStatusEnum.MANUAL_DIRECT_IDLE && seatStatus.value !== SeatStatusEnum.HUMAN_MACHINE_IDLE) {
      if (seatStatus.value === SeatStatusEnum.IN_REST || seatStatus.value === SeatStatusEnum.OFF_LINE) {
        ElMessage.warning('坐席休息中，无法签入任务')
      } else {
        // 坐席在任意状态都可以签入任务
        // 签入任务
        const taskIdSet = new Set(taskList.map((task: TaskManageItem) => {
          return task?.id ?? -1
        }))
        taskIdSet.delete(-1)
        const taskIdList: number[] = Array.from(taskIdSet)
        await seatWorkbenchAccountModel.checkIn({
          taskIds: taskIdList ?? [],
          callSeatStatus: seatStatus.value || SeatStatusEnum.HUMAN_MACHINE_IDLE,
        })
        ElMessage({
          message: `${fsAccount.account} 成功更新签入任务`,
          type: 'success',
        })
        ElMessage({
          message: `通话中请勿强制刷新、关闭页面`,
          type: 'warning',
        })
        // 更新坐席信息
        await updateSeatInfo()
        report({
          action: (actionType === '手动') ? SeatLogActionEnum['关联任务手动签入'] : SeatLogActionEnum['关联任务自动签入'],
          desc: '关联任务启动',
        })
        // 更新坐席统计数据
        needUpdateWorkbenchStatistics.value = true
      }

      // 关闭通知
      notification.close(key)
      // 移出通知队列
      const index = notificationKeyList.findIndex((item: string) => {
        return item === key
      })
      notificationKeyList.splice(index, 1)
    }

    notification.warning({
      message: '关联任务已启动',
      description: str,
      class: 'linked-task-start-notification',
      onClose: () => {
        // 移出通知队列
        const index = notificationKeyList.findIndex((item: string) => {
          return item === key
        })
        notificationKeyList.splice(index, 1)
      },
      btn: () =>
        h(
          Button,
          {
            type: 'primary',
            size: 'large',
            onClick: () => {
              handleCheckInTask('手动').then(() => {
              }).catch(() => {
              })
            },
          },
          { default: () => '签入' },
        ),
      key,
      getContainer: () => {
        return document.body
      },
      duration: 5,
      placement: 'topRight',
    })

    // 如果开启了自动签入任务，那么显示通知后，关闭通知并签入任务
    if (seatSetting.value.autoCheckInTask) {
      setTimeout(() => {
        handleCheckInTask('自动').then(() => {
        }).catch(() => {
        })
      }, 1000)
    }
  }

  // ---------------------------------------- 心跳检测 结束 ----------------------------------------

  // ---------------------------------------- 坐席上下线 开始 ----------------------------------------

  /**
   * 检测上线条件
   * 坐席账号，网络状况，浏览器版本，音频输出输入设备等
   */
  const checkOnlineCondition = async () => {
    // 先集中判断，记录检测结果
    // 然后把不通过的条件列出来弹窗提示

    // 结果记录
    const resultList = {
      seatAccount: false,
      janusService: false,
      browser: false,
      webRTC: false,
      audioOutput: false,
      audioInput: false,
    }
    // 提示文本
    const hintList = {
      seatAccount: '坐席账号不存在，请加入坐席组',
      janusService: 'Janus SIP 通话服务没有正确运行，请检查网络设置',
      browser: '浏览器不是谷歌 Google Chrome 浏览器，请更换为谷歌 Google Chrome 浏览器',
      webRTC: '浏览器不支持 WebRTC，请检查 WebRTC 是否被禁用、升级浏览器或者使用其它浏览器',
      audioOutput: '无法播放音频，请检查耳机/扬声器权限和耳机/扬声器音量',
      audioInput: '无法使用麦克风，请检查麦克风权限和麦克风音量',
    }

    // 检测坐席账号
    resultList.seatAccount = !!currentSeat.value.id

    // 检测Janus服务
    resultList.janusService = !!sipCallList[instanceId]

    // 检测浏览器类型
    resultList.browser = getBrowserType() === 'Chrome'

    // 检测浏览器WebRTC
    resultList.webRTC = janusUtil.isWebrtcSupported()

    // 检测音频输出设备（扬声器）
    try {
      const deviceList: MediaDeviceInfo[] = await navigator.mediaDevices.enumerateDevices() || []
      resultList.audioOutput = deviceList.some(device => device.kind === 'audiooutput') || false
    } catch (e) {
      resultList.audioOutput = false
    }

    // 检测音频输入设备（麦克风）
    try {
      const stream: MediaStream = await navigator.mediaDevices.getUserMedia({
        audio: true,
        video: false
      })
      // 检测完立马关闭麦克风，不需要占用过多时间
      stream.getTracks().forEach((track: MediaStreamTrack) => {
        track?.stop()
      })
      resultList.audioInput = true
    } catch (e) {
      resultList.audioInput = false
    }

    // 筛选出检测不通过的
    const problemList = Object.keys(resultList).filter((prop: string) => {
      return !resultList[<keyof typeof resultList>prop]
    })

    if (problemList.length) {
      console.warn('不符合上线条件', problemList)
      ElMessageBox({
        title: '不符合上线条件',
        message: h('ol', null, problemList.map((prop: string) => {
          return h('li', null, hintList[<keyof typeof hintList>prop])
        })),
        confirmButtonText: '关闭',
        closeOnClickModal: false,
        dangerouslyUseHTMLString: true,
      }).then(() => {
      }).catch(() => {
      })
      throw new Error()
    }
  }
  /**
   * 坐席账号上线
   * @param {boolean} changeValue 手动修改seatOnline的值，而不是等开关UI组件自动更新
   * @param {boolean} needUpdateSeatInfo 需要更新坐席信息
   */
  const online = async (changeValue: boolean = false, needUpdateSeatInfo: boolean = true) => {
    // 更新坐席信息
    if (needUpdateSeatInfo) {
      try {
        await updateSeatInfo()
      } catch (e) {
        throw new Error()
      }
    }

    // 检测上线条件
    try {
      await checkOnlineCondition()
    } catch (e) {
      throw new Error()
    }

    // 没有坐席信息
    if (currentSeat.value.id === undefined || currentSeat.value.id === null) {
      ElMessage({
        message: '坐席不存在，无法上线',
        type: 'error',
      })
      throw new Error('坐席不存在，无法上线')
    }

    // 获取账号绑定信息
    const res = <SeatAccountBindInfo>await seatServerModel.getBindInfo({
      seatId: currentSeat.value.id!,
    })
    console.log('上线前检查FS账号信息', JSON.parse(JSON.stringify(res)))
    if (!res) {
      throw new Error('FS账号信息为空，无法上线，请联系管理员重新绑定FS账号')
    }
    // 更新FS账号
    fsAccount.account = res.fsUser
    fsAccount.password = res.fsUserPasswd
    fsAccount.ip = res.fsIp
    fsAccount.port = res.fsPort ?? null
    if (res.fsPort) {
      fsAccount.address = res.fsIp + ':' + res.fsPort
    } else {
      fsAccount.address = fsAccount.ip = res.fsIp
    }
    console.log('FS账号', fsAccount)
    if (!fsAccount.address || !fsAccount.account || !fsAccount.password) {
      throw new Error('FS账号信息不正确，无法上线，请联系管理员重新绑定FS账号')
    }

    try {
      // 如果有签入任务，则上线时，坐席状态要切换成人机协同空闲中，其余情况都默认人工直呼空闲中
      // 坐席状态
      const callSeatStatus = seatTaskList.value.length ? SeatStatusEnum.HUMAN_MACHINE_IDLE : SeatStatusEnum.MANUAL_DIRECT_IDLE
      // 调用上线接口
      await seatWorkbenchAccountModel.online({
        callSeatStatus: callSeatStatus,
      })
      report({
        action: SeatLogActionEnum['上线'],
        desc: `发送上线请求`,
      })
      // 注册 Janus SIP / FS 账号
      register()
      // 是否手动更新坐席在线
      if (changeValue) {
        seatInfoStore.updateSeatOnline(true)
      }
      // 更新坐席状态
      seatInfoStore.updateSeatStatus(callSeatStatus)
      // 更新通话类型
      updateCallType()
      // 关闭未上线弹窗
      dialogOfflineVisible.value = false
    } catch (e: any) {
      seatInfoStore.updateSeatOnline(false)
      report({
        type: SeatLogTypeEnum['错误'],
        action: SeatLogActionEnum['上线'],
        desc: `发送上线请求 但是接口报错` + (e?.msg ? ('：' + e?.msg) : ''),
      })
      throw new Error()
    }
  }
  /**
   * 请求接口下线
   */
  const requestOffline = async () => {
    try {
      await seatWorkbenchAccountModel.offline()
      ElMessage.success(`${currentSeat.value.account} 下线成功`)
      await reportSync({
        action: SeatLogActionEnum['下线'],
        desc: `发送下线请求`,
      })
      registered.value = false
      stopHeartbeatTimer()
      resetWorkbench(false)
      // 清屏通知
      ElNotification.closeAll()
      notificationKeyList.forEach((key: string) => {
        notification.close(key)
      })
      // 断开websocket连接
      ws?.disconnect()
    } catch (e: any) {
      report({
        type: SeatLogTypeEnum['错误'],
        action: SeatLogActionEnum['下线'],
        desc: `发送下线请求 但是接口报错` + (e?.msg ? ('：' + e?.msg) : ''),
      })
      throw e
    }
  }
  /**
   * 坐席账号下线
   */
  const offline = async () => {
    // 网络断开，直接全部下线
    if (!navigator.onLine) {
      busy.value = false
      registered.value = false
      seatInfoStore.updateSeatOnline(false)
      await requestOffline()
      resetWorkbench()
      return
    }

    // 有电话就挂
    if (busy.value) {
      await hangup()
    }

    // FS账号还注册着的就立马注销
    if (registered.value) {
      sipCallList[instanceId]?.send({
        message: {
          request: 'unregister',
        },
      })
      console.log('***', '发送动作请求', '注销')
      report({
        type: SeatLogTypeEnum['详细'],
        action: SeatLogActionEnum['Janus请求'],
        desc: 'unregister',
      })
    } else {
      await requestOffline()
    }
  }
  /**
   * 强制下线
   */
  const forceOffline = () => {
    report({
      type: SeatLogTypeEnum['详细'],
      action: SeatLogActionEnum['坐席状态变化'],
      desc: '强制下线',
    })
    busy.value = false
    registered.value = false
    seatInfoStore.updateSeatOnline(false)
  }

  // ---------------------------------------- 坐席上下线 结束 ----------------------------------------

  // ---------------------------------------- 条件检查 开始 ----------------------------------------

  /**
   * 检测Janus服务是否正常
   */
  const checkJanus = () => {
    if (!sipCallList[instanceId]) {
      ElMessage({
        message: '通话Janus服务没有正确初始化',
        type: 'warning',
      })
      return false
    }
    return true
  }
  /**
   * 检测是否注册FS账号
   */
  const checkRegistered = () => {
    if (!registered.value) {
      ElMessage({
        message: 'FS账号正在注册中，请稍后重试。如果长时间未注册，请刷新页面或检查网络设置',
        type: 'warning',
        duration: 5000,
      })
      return false
    }
    return true
  }
  /**
   * 检测是否通话中
   */
  const checkBusy = () => {
    if (busy.value) {
      ElMessage({
        message: '正在进行通话',
        type: 'warning',
      })
      return false
    }
    return true
  }

  // ---------------------------------------- 条件检查 结束 ----------------------------------------

  // ---------------------------------------- 呼叫弹窗 开始 ----------------------------------------

  // -------------------- 呼叫动作 开始 --------------------

  // 是否显示呼叫弹窗
  const dialogCallingVisible = ref(false)
  // 呼叫时长秒数
  const callingSecond = ref<number>(0)
  // 呼叫最大秒数
  const callingMaxSecond = ref<number>(180)
  // 呼叫计时器
  let callingTimer: number | null = null
  // 呼叫下一个的动作类型
  // 不呼叫 空字符串
  // 手动
  // 自动
  const needCallNext = ref<string>('')

  /**
   * 发起呼叫
   * @param {ClueItem} row 当前线索
   */
  const launchCall = async (row: ClueItem) => {
    if (!checkOnline()) {
      dialogOfflineVisible.value = true
      return
    }
    if (!checkStatus()) {
      return
    }
    if (!checkJanus() || !checkRegistered() || !checkBusy()) {
      return
    }

    // 更新当前线索
    updateCurrentClue(row)
    // 显示呼叫弹窗
    showCalling()

    report({
      action: SeatLogActionEnum['人工直呼-呼叫'],
      desc: '人工直呼 发起呼叫',
      phone: currentClue.value.phone,
    })

    try {
      // 状态更新到人工直呼
      seatInfoStore.updateSeatStatus(SeatStatusEnum.MANUAL_DIRECT_CALLING)

      // 检查坐席组是否有人工线路
      if (!currentSeatTeam.value.tenantLineNumber) {
        throw new Error('无人工线路或线路已停用')
      }

      // 通知接口发起呼叫
      const res = <SeatCallManualRes>await seatWorkbenchCallModel.launchManualDirectCall({
        clueId: row.id ?? null,
      })

      // 当前呼叫号码被黑名单限制和频率限制
      if (res.filterReason?.includes('black')) {
        throw new Error('呼叫受限')
      } else if (res.filterReason?.includes('frequent')) {
        throw new Error('频率受限')
      }
      // 当前呼叫号码没有正确的通话信息
      if (!res.callId) {
        throw new Error('没有正确的通话信息callId')
      }

      // 更新通话记录
      callRecordForManualDirect.value = res ?? {}
      // 更新ID
      callInfo.value.callId = res.callId || ''

      // 更新通话类型
      updateCallType({
        type: SeatCallTypeEnum.DIRECT
      })

      getCallCarryInfo().then(() => {
      }).catch(() => {
      })

      ElMessage({
        message: row?.clueUniqueId ? '正在呼叫' : `正在呼叫 ${row?.clueUniqueId ?? ''}`,
        type: 'success',
      })
      ElMessage({
        message: `通话中请勿强制刷新、关闭页面`,
        type: 'warning',
      })
    } catch (e: any) {
      // 隐藏呼叫弹窗
      hideCalling()
      // 显示呼叫失败弹窗
      let errStr = e?.message ?? '发起呼叫接口错误'
      if (e?.data?.message) {
        errStr = '发起呼叫接口错误：' + e?.data?.message
      }
      showDialogCallFail(errStr)
      // 处理呼叫失败
      await handleCallFail(row)
    }
  }
  /**
   * 显示呼叫弹窗
   */
  const showCalling = () => {
    // 显示弹窗
    dialogCallingVisible.value = true
    // 开始呼叫计时
    startCallingTimer()
  }
  /**
   * 隐藏呼叫弹窗
   */
  const hideCalling = () => {
    // 关闭弹窗
    dialogCallingVisible.value = false
    // 结束呼叫计时
    stopCallingTimer()
  }
  /**
   * 处理呼叫失败
   * @param {ClueItem} row 当前线索
   */
  const handleCallFail = async (row: ClueItem) => {
    try {
      // 通知接口呼叫失败
      await seatWorkbenchCallModel.launchFailManualDirectCall({
        clueId: row.id ?? currentClue.value.id ?? null,
        waitMSec: callingSecond.value ?? null,
      })
    } catch (e) {
    } finally {
      if (seatStatus.value === SeatStatusEnum.MANUAL_DIRECT_CALLING) {
        // 恢复空闲状态
        seatInfoStore.updateSeatStatusByTaskList()
        updateCallType()
      }
    }
  }
  /**
   * 呼叫线索列表下一个
   * @param type 手动 自动 默认手动
   */
  const callNext = async (type?: string) => {
    // 签入任务为空，才是真正的人工直呼工作模式
    // 签入任务不为空，是人机协同的工作模式，临时进行人工直呼，不需要提供拨打下一个
    if (seatTaskList.value.length) {
      ElMessage.warning('当前坐席不是人工直呼，不支持呼叫下一个')
      return
    }

    // 检查坐席组是否有人工线路
    if (!currentSeatTeam.value.tenantLineNumber) {
      ElMessage.warning('无人工线路或线路已停用')
      return
    }

    // 更新线索列表 TODO
    needUpdateClueList.value = true

    // 找到跟进次数为0的但不是当前的线索继续拨打
    // 如果列表到头了就提示已到底
    const currentIndex = clueList.value.findIndex((item: ClueItem) => {
      return item?.id === currentClue.value.id
    })
    // console.log('currentIndex', currentIndex)
    const nextClue = clueList.value.find((item: ClueItem, index) => {
      return item?.id !== currentClue.value.id && (
        (
          Number(item?.followCount) === 0
          || item?.followCount === undefined
          || item?.followCount === null
        )
        && index > currentIndex
      )
    })
    if (nextClue) {
      report({
        action: SeatLogActionEnum['人工直呼-拨打下一个'],
        desc: '人工直呼 ' + (type ?? '手动') + '拨打下一个',
        phone: nextClue.phone ?? '',
      })
      await launchCall(nextClue)
    }
  }

  // -------------------- 呼叫动作 结束 --------------------

  // -------------------- 呼叫计时器 开始 --------------------

  /**
   * 呼叫计时器回调
   */
  const callingTimerCallback = async () => {
    callingSecond.value++
    // 如果呼叫秒数超过最大秒数，停止呼叫，并显示呼叫失败
    if (callingSecond.value >= callingMaxSecond.value) {
      hideCalling()
      showDialogCallFail('呼叫超时')
      await handleCallFail(currentClue.value)
    }
  }
  /**
   * 开始计时呼叫时长
   */
  const startCallingTimer = () => {
    if (callingTimer) {
      return
    }
    callingSecond.value = 0
    callingTimer = <number><unknown>setInterval(callingTimerCallback, 1000)
  }
  /**
   * 结束计时呼叫时长
   */
  const stopCallingTimer = () => {
    if (!callingTimer) {
      return
    }
    clearInterval(callingTimer)
    callingTimer = null
  }

  // -------------------- 呼叫计时器 结束 --------------------

  // ---------------------------------------- 呼叫弹窗 结束 ----------------------------------------

  // ---------------------------------------- 呼叫失败弹窗 开始 ----------------------------------------

  // 呼叫失败弹窗 显示隐藏
  const dialogCallFailVisible = ref(false)
  // 呼叫失败弹窗 失败原因
  const callFailReason = ref<string>('')
  // 呼叫失败弹窗 倒计时 定时器
  let timerDialogCallFail: number | null = null
  // 呼叫失败弹窗 下次呼叫倒计时 开始秒数
  const callNextCountdownSecondDefault: number = 1
  // 呼叫失败弹窗 下次呼叫倒计时 剩余秒数
  const callNextCountdownSecond = ref<number>(callNextCountdownSecondDefault)

  /**
   * 呼叫失败弹窗 显示
   */
  const showDialogCallFail = (causeString?: string) => {
    // 关闭呼叫弹窗
    hideCalling()
    // 显示呼叫失败弹窗
    dialogCallFailVisible.value = true
    // 更新呼叫失败原因
    callFailReason.value = causeString ?? ''
    report({
      type: SeatLogTypeEnum['警告'],
      action: SeatLogActionEnum['人工直呼-呼叫失败'],
      desc: '人工直呼 呼叫失败 ' + (callFailReason.value ?? '') + ' 呼叫时长 ' + (callingSecond.value ?? ''),
      phone: currentClue.value.phone,
    })
    // 人工直呼时
    if (!seatTaskList.value.length && seatSetting.value.autoCallNext) {
      // 开启自动呼叫下一个倒计时
      startTimerDialogCallFail()
    }
  }
  /**
   * 呼叫失败弹窗 隐藏
   */
  const hideDialogCallFail = () => {
    stopTimerDialogCallFail()
    dialogCallFailVisible.value = false
    // 恢复默认值
    callNextCountdownSecond.value = callNextCountdownSecondDefault
    callFailReason.value = ''
    // 清空通话信息
    clearCallInfo()
  }
  /**
   * 呼叫失败弹窗 点击取消
   */
  const onClickCancelCall = () => {
    // 关闭弹窗
    hideDialogCallFail()
    setTimeout(() => {
      // 重置计时
      callingSecond.value = 0
    }, 500)
    // 更新线索列表
    needUpdateClueList.value = true
  }
  /**
   * 呼叫失败弹窗 点击重拨
   */
  const onClickRetryCall = () => {
    // 关闭弹窗
    hideDialogCallFail()
    // 呼叫
    setTimeout(async () => {
      await launchCall(currentClue.value)
    }, 500)
  }
  /**
   * 呼叫失败弹窗 点击拨打下一个
   */
  const onClickCallNext = () => {
    // 关闭弹窗
    hideDialogCallFail()
    // 呼叫
    setTimeout(async () => {
      await callNext('手动')
    }, 500)
  }
  /**
   * 呼叫失败弹窗 定时器 业务处理
   */
  const handleTimerDialogCallFail = () => {
    // 秒数递减
    callNextCountdownSecond.value--
    // 如果倒计时结束了
    if (callNextCountdownSecond.value <= 0) {
      // 关闭定时器
      stopTimerDialogCallFail()
      // 隐藏呼叫失败弹窗
      hideDialogCallFail()
      // 弹窗关闭有动画，弹窗里展示了电话号码，
      // 如果立马拨打下一个，下一个电话号码会被看到，
      // 所以加上延迟，等弹窗动画结束，完全关闭后，再更新号码拨打下一个
      if (seatSetting.value.autoCallNext) {
        setTimeout(async () => {
          // 自动拨打下一个
          await callNext('自动')
        }, 500)
      }
    }
  }
  /**
   * 呼叫失败弹窗 开启倒计时
   */
  const startTimerDialogCallFail = () => {
    // 清空旧的定时器
    stopTimerDialogCallFail()
    timerDialogCallFail = <number><unknown>setInterval(handleTimerDialogCallFail, 1000)
  }
  /**
   * 呼叫失败弹窗 关闭倒计时
   */
  const stopTimerDialogCallFail = () => {
    // console.log('呼叫失败弹窗 关闭倒计时')
    try {
      clearInterval(<number>timerDialogCallFail)
    } catch (e) {
    } finally {
      timerDialogCallFail = null
    }
  }

  // ---------------------------------------- 呼叫失败弹窗 结束 ----------------------------------------

  // ---------------------------------------- 来电弹窗 开始 ----------------------------------------

  // 介入 正在加载
  const loadingIntervene = ref<boolean>(false)
  // 介入 节流锁
  const throttleIntervene = new Throttle(loadingIntervene)

  // 来电等待接听倒计时 计时器
  let timer: any = null

  /**
   * 显示新来电提示
   */
  const showIncomingCall = () => {
    // 播放铃声
    ringtoneDom.value?.play().then(() => {
    }).catch((err: any) => {
      console.error('无法播放转人工弹窗铃声', err)
    })
    // 重置倒计时
    leftTransferSecond.value = totalTransferSecond.value
    // 显示通话抽屉
    seatInfoStore.updateSeatPage(SeatPageEnum.PHONE)
    startWaitingTimer()
  }
  /**
   * 隐藏新来电提示
   * @param {boolean} closeDrawer 关闭通话抽屉
   */
  const hideIncomingCall = (closeDrawer: boolean = true) => {
    stopWaitingTimer()
    ringtoneDom.value?.pause()
    if (closeDrawer) {
      // 关闭通话抽屉
      seatInfoStore.updateSeatPage(SeatPageEnum.CLUE)
    }
  }
  /**
   * 开启倒计时
   */
  const startWaitingTimer = () => {
    if (timer) {
      return
    }
    timer = setInterval(async () => {
      leftTransferSecond.value--
      // 如果倒计时结束了
      if (leftTransferSecond.value <= 0) {
        // 坐席账号拒绝接听
        decline()
        // 关闭新来电提示和计时器
        hideIncomingCall()
      }
    }, 1000) ?? 0
  }
  /**
   * 关闭倒计时
   */
  const stopWaitingTimer = () => {
    if (!timer) {
      return
    }
    clearInterval(timer)
    timer = null
  }
  /**
   * 处理接听
   * 人机协同监听、人机协同接听（接管）
   * @param {boolean} autoAccept 是否自动接听
   */
  const handleAccept = async (autoAccept: boolean = false) => {
    // 关闭新来电提示，并且不关闭抽屉
    hideIncomingCall(false)

    // 接听
    if (sipCallAction) {
      try {
        sipCallAction(sipCallActionParams)
      } catch (e) {
      }
    }
    // 打开通话界面
    seatInfoStore.updateSeatPage(SeatPageEnum.PHONE)

    const currentTime = dayjs().format('YYYY-MM-DD HH:mm:ss')

    if (callType.value === SeatCallTypeEnum.MONITOR) {
      // 人机协同 监听再介入

      // 人机协同 监听
      seatInfoStore.updateSeatStatus(SeatStatusEnum.HUMAN_MACHINE_LISTEN)
      await seatWorkbenchCallModel.startMonitorHumanMachine({
        recordId: callRecordForHumanMachine.value?.recordId,
        startMonitorTime: currentTime
      })

      report({
        action: SeatLogActionEnum['人机协同-监听'],
        desc: autoAccept ? '人机协同 自动监听' : '人机协同 手动监听',
        phone: callRecordForHumanMachine.value.phone,
      })

      // 介入按钮延迟启用，防止监听后立马介入太快了
      interactionStore.startDelayInterveneTimer()

    } else if (callType.value === SeatCallTypeEnum.ANSWER) {
      // 人机协同 接管（无监听直接介入）
      report({
        action: SeatLogActionEnum['人机协同-接管'],
        desc: autoAccept ? '人机协同 自动接管' : '人机协同 手动接管',
        phone: callRecordForHumanMachine.value.phone,
      })
    }

    ElMessage({
      type: 'success',
      message: `电话已接通`,
      duration: 1000,
    })
    busy.value = true

    // 通知FS接听成功
    // 参数里的IP地址和端口，不是坐席绑定的FS账号的信息，而是每次通话从接口响应里拿到的信息
    const acceptParams = {
      seatId: currentSeat.value.id,
      callId: callInfo.value.callId,
      aiCallIp: callInfo.value.aiCallIp,
      aiCallPort: callInfo.value.aiCallPort,
    }
    const [acceptError, _] = <[any, any]>await to(seatServerModel.accept(acceptParams))
    report({
      type: SeatLogTypeEnum[acceptError ? '错误' : '详细'],
      action: SeatLogActionEnum['通知FS'],
      desc: '通知FS接口seat-deal' + (acceptError ? '发生错误' : ''),
      phone: callRecordForHumanMachine.value.phone,
      params: acceptParams || {},
    })
    trace({
      page: '通知FS接口seat-deal' + (acceptError ? '发生错误' : ''),
      params: acceptParams,
    })
  }

  // ---------------------------------------- 来电弹窗 结束 ----------------------------------------

  // ---------------------------------------- 退出监听弹窗 开始 ----------------------------------------

  // 是否显示退出监听弹窗
  const dialogExitMonitorVisible = ref(false)

  // ---------------------------------------- 退出监听弹窗 结束 ----------------------------------------

  // ---------------------------------------- 接待时长 开始 ----------------------------------------

  // 接待时长警告阈值（分钟）
  const RECEPTION_WARNING_THRESHOLD_MINUTES = 25
  // 接待时长警告阈值（秒）
  const RECEPTION_WARNING_THRESHOLD_SECONDS = RECEPTION_WARNING_THRESHOLD_MINUTES * 60

  // 接待时长，定时器
  let receptionTimer: number | null = null
  // 接待时长，秒
  let receptionSecond = ref(0)
  // 接待时长，开始日期时间
  let receptionStartDatetime = dayjs()
  // 是否已显示接待时长警告提示
  let hasReceptionWarningShown = false

  /**
   * 接待时长，重置定时器
   */
  const resetReceptionTimer = () => {
    receptionSecond.value = 0
    hasReceptionWarningShown = false
  }
  /**
   * 接待时长，处理定时器
   */
  const handleReceptionTimer = () => {
    receptionSecond.value = dayjs().diff(receptionStartDatetime, 'second') ?? 0

    // 检查是否超过接待时长警告阈值且未显示过提示
    if (receptionSecond.value >= RECEPTION_WARNING_THRESHOLD_SECONDS && !hasReceptionWarningShown) {
      hasReceptionWarningShown = true

      // 创建警告消息
      ElMessageBox({
        title: '',
        message: '根据业务要求，接待时长不宜超过30分钟，<br>请尽快结束当前通话！',
        confirmButtonText: '关闭',
        closeOnClickModal: false,
        dangerouslyUseHTMLString: true,
      }).then(() => {
      }).catch(() => {
      })
    }
  }
  /**
   * 接待时长，关闭定时器
   */
  const closeReceptionTimer = () => {
    if (typeof receptionTimer === 'number') {
      clearInterval(receptionTimer)
    }
    receptionTimer = null
  }
  /**
   * 接待时长，开启定时器
   */
  const openReceptionTimer = () => {
    closeReceptionTimer()
    resetReceptionTimer()
    receptionStartDatetime = dayjs()
    receptionTimer = <number><unknown>setInterval(handleReceptionTimer, 1000)
  }
  /**
   * 接待时长，快进到警告阈值前10秒（测试环境专用）
   */
  const fastForwardReceptionTimer = () => {
    if (receptionTimer) {
      const fastForwardSeconds = RECEPTION_WARNING_THRESHOLD_SECONDS - 10
      receptionStartDatetime = dayjs().subtract(fastForwardSeconds, 'second')
      hasReceptionWarningShown = false
    }
  }

  // ---------------------------------------- 接待时长 结束 ----------------------------------------

  // ---------------------------------------- 未上线提示弹窗 开始 ----------------------------------------

  // 是否显示未上线提示弹窗
  const dialogOfflineVisible = ref(false)

  // ---------------------------------------- 未上线提示弹窗 结束 ----------------------------------------

  // ---------------------------------------- WebSocket连接 开始 ----------------------------------------

  let ws: wsUtil | null = null
  let wsConfig: WebSocketConfig = {
    url: '',
    onOpen: () => {
      if (wsErrorTimes) {
        ElMessage.success('WebSocket 连接成功')
      }
      wsErrorTimes = 0
      report({
        type: SeatLogTypeEnum['详细'],
        action: SeatLogActionEnum['WebSocket连接'],
        desc: 'WebSocket连接已打开'
      })
    },
    onError: (e: any) => {
      report({
        type: SeatLogTypeEnum['错误'],
        action: SeatLogActionEnum['WebSocket连接'],
        desc: 'WebSocket连接发生错误：' + e.toString()
      })
    },
    onClose: async () => {
      if (registered.value) {
        ElMessage.error('WebSocket 断开连接，请检查网络')
        await handleWebSocketClose()
      }
      report({
        type: SeatLogTypeEnum['详细'],
        action: SeatLogActionEnum['WebSocket连接'],
        desc: 'WebSocket连接已关闭'
      })
    },
    onMessage: async (event: any) => {
      const msg: string = event?.data
      let obj: SeatWebSocketMsg
      try {
        obj = JSON.parse(msg) ?? {}
      } catch (e) {
        obj = {}
      }

      // 心跳保活不记录，其他websocket消息都记录
      if (obj?.status !== 'keepalive') {
        report({
          type: SeatLogTypeEnum['详细'],
          action: SeatLogActionEnum['WebSocket响应'],
          response: obj,
        })
      }

      if (obj?.status === 'keepalive') {
        // 刷新心跳保活
        ws?.resetKeepalive()
      } else if (obj?.noticeType === 'direct-call-status') {
        // 人工直呼呼叫状态
        await handleCallStatus(obj)
      } else if (obj?.noticeType === 'event-push') {
        // 客户状态记录
        handleReceiveEvent(obj)
      } else if (obj?.noticeType === 'phone-carry-info') {
        // 通话携带信息
        updateCallCarryInfo(obj)
      }
    },
    keepalive: true,
    keepaliveSecond: 5,
    handleKeepalive: () => {
      const msg: SeatWebSocketMsg = {
        action: 'keepalive',
      }
      ws?.send(JSON.stringify(msg))
    },
  }

  // ---------------------------------------- WebSocket连接 结束 ----------------------------------------

  // ---------------------------------------- 人工直呼 呼叫状态 开始 ----------------------------------------

  /**
   * 处理得到的呼叫状态
   * @param {SeatWebSocketMsg} obj 呼叫状态信息
   */
  const handleCallStatus = async (obj: SeatWebSocketMsg) => {
    console.log('------', 'websocket', 'message', '当前callId', callInfo.value.callId)
    // if (obj?.callId !== callInfo.value.callId) {
    //   // 确保查询的呼叫状态结果是当前呼叫的，不能发生错乱
    //   console.warn('------', 'websocket', 'message', '收到消息', 'callId不属于当前线索/通话的callId', '当前callId', callInfo.value.callId)
    //   return
    // } else if (obj?.callStatus === '7') {
    if (obj?.callStatus === '7') {
      // 已接通
      // 关闭呼叫弹窗
      hideCalling()
      hideDialogCallFail()
    } else if (obj?.callStatus !== '7') {
      // 未接通

      // 坐席日志上报
      if (obj?.causeString?.includes('未呼通')) {
        // 坐席主动挂断
        // 更新坐席状态和通话类型
        // 这两个动作提前到发送ws消息时就执行了，而不是这里等ws返回消息
        report({
          action: SeatLogActionEnum['人工直呼-挂断'],
          desc: '人工直呼 坐席挂断',
          phone: currentClue.value.phone,
        })

        // 延迟执行，防止消息返回太快，但是还没有显示呼叫弹窗和通话抽屉
        await new Promise(resolve => setTimeout(resolve, 1000))

        // 关闭呼叫弹窗
        hideCalling()
        hideDialogCallFail()
        // 关闭通话抽屉，回到线索页
        seatInfoStore.updateSeatPage(SeatPageEnum.CLUE)
      } else if (obj?.cause || obj?.causeString) {
        // 客户挂断、客户未接听等
        report({
          action: SeatLogActionEnum['人工直呼-挂断'],
          desc: '人工直呼 客户挂断/未接听 ' + (obj?.cause || obj?.causeString),
          phone: currentClue.value.phone,
        })
      } else {
        // 其他情况
        report({
          action: SeatLogActionEnum['人工直呼-挂断'],
          desc: '人工直呼 其他情况挂断',
          phone: currentClue.value.phone,
        })
      }

      // 显示呼叫失败弹窗
      showDialogCallFail(obj?.causeString || obj?.cause || '')
      // 处理呼叫失败
      await handleCallFail(currentClue.value)
    }
  }

  // ---------------------------------------- 人工直呼 呼叫状态 结束 ----------------------------------------

  // ---------------------------------------- 通话提示音 开始 ----------------------------------------

  // 接通提示音 DOM
  const acceptAudio = ref<HTMLAudioElement>()
  // 挂断提示音 DOM
  const hangupAudio = ref<HTMLAudioElement>()

  /**
   * 初始化通话提示音
   */
  const initDialAudio = () => {
    console.log('初始化通话提示音')
    acceptAudio.value = new Audio()
    acceptAudio.value.src = '/accept.wav'
    acceptAudio.value.loop = false

    hangupAudio.value = new Audio()
    hangupAudio.value.src = '/hangup.wav'
    hangupAudio.value.loop = false
  }
  /**
   * 播放接通提示音
   */
  const playAcceptAudio = () => {
    if (!acceptAudio.value) {
      return
    }
    acceptAudio.value.currentTime = 0
    acceptAudio.value.play().then(() => {
    }).catch(() => {
    })
  }
  /**
   * 播放挂断提示音
   */
  const playHangupAudio = () => {
    if (!hangupAudio.value) {
      return
    }
    hangupAudio.value.currentTime = 0
    hangupAudio.value.play().then(() => {
    }).catch(() => {
    })
  }

  // ---------------------------------------- 通话提示音 结束 ----------------------------------------

  // ---------------------------------------- 新分配线索通知 开始 ----------------------------------------

  // 新分配线索通知 DOM唯一索引
  let newClueNotificationKey: string = ''
  // 新分配线索通知 持续时长 单位秒
  const NEW_CLUE_NOTIFICATION_SECOND: number = 20

  /**
   * 新分配线索通知 通知提示
   * @param {number} newClueCount 新线索数量
   */
  const notifyNewClue = (newClueCount: number = 0) => {
    newClueNotificationKey = 'new-clue-notification' + '-' + Date.now()

    notification.warning({
      message: `分配到【${newClueCount}】条新线索`,
      description: '请刷新线索列表进行查看',
      class: 'new-clue-notification',
      key: newClueNotificationKey,
      getContainer: () => {
        return document.body
      },
      duration: NEW_CLUE_NOTIFICATION_SECOND,
      placement: 'topRight',
    })
  }
  /**
   * 新分配线索通知 关闭通知提示
   */
  const closeNotifyNewClue = () => {
    notification.close(newClueNotificationKey)
  }

  // ---------------------------------------- 新分配线索通知 结束 ----------------------------------------

  // ---------------------------------------- 断线重连 开始 ----------------------------------------

  // 最大尝试重连次数
  const MAX_RECONNECT_TIMES: number = 3
  // 每次重连失败间隔时长，单位秒
  const RECONNECT_WAIT_SECOND: number = 1

  // -------------------- Janus错误 开始 --------------------

  // Janus错误尝试次数
  let janusErrorTimes = 0

  /**
   * 处理Janus断开连接等错误
   */
  const handleJanusError = async () => {
    janusErrorTimes++
    // 网络断开或者重连失败超过最大次数，立马下线
    if (!navigator.onLine || janusErrorTimes > MAX_RECONNECT_TIMES) {
      ElMessage.error('Janus SIP 通话服务多次尝试重连均失败，即将下线')
      console.error('Janus SIP 通话服务多次尝试重连均失败，即将下线')
      report({
        type: SeatLogTypeEnum['错误'],
        action: SeatLogActionEnum['Janus连接'],
        desc: '多次尝试重连均失败，即将下线'
      })
      await offline()
      return
    }

    // 节流锁，等待一会再重连
    await new Promise((resolve) => {
      setTimeout(resolve, RECONNECT_WAIT_SECOND * 1000)
    })

    // 重连
    ElMessage.warning('正在尝试重连 Janus SIP 通话服务')
    console.warn('正在尝试重连 Janus SIP 通话服务')
    report({
      type: SeatLogTypeEnum['详细'],
      action: SeatLogActionEnum['Janus连接'],
      desc: '正在尝试重连'
    })
    createJanus()
  }

  // -------------------- Janus错误 结束 --------------------

  // -------------------- WebSocket错误 开始 --------------------

  // WebSocket错误次数
  let wsErrorTimes = 0

  /**
   * 处理WebSocket断开连接等错误
   */
  const handleWebSocketClose = async () => {
    wsErrorTimes++
    // 网络断开或者重连失败超过最大次数，立马下线
    if (!navigator.onLine || wsErrorTimes > MAX_RECONNECT_TIMES) {
      ElMessage.error('WebSocket 多次尝试重连均失败，即将下线')
      console.error('WebSocket 多次尝试重连均失败，即将下线')
      report({
        type: SeatLogTypeEnum['错误'],
        action: SeatLogActionEnum['WebSocket连接'],
        desc: '多次尝试重连均失败，即将下线'
      })
      await offline()
      return
    }

    // 节流锁，等待一会再重连
    await new Promise((resolve) => {
      setTimeout(resolve, RECONNECT_WAIT_SECOND * 1000)
    })

    // 重连
    ws = new wsUtil(wsConfig)
    ElMessage.warning('正在尝试重连 WebSocket')
    console.warn('正在尝试重连 WebSocket')
    report({
      type: SeatLogTypeEnum['详细'],
      action: SeatLogActionEnum['WebSocket连接'],
      desc: '正在尝试重连'
    })
    ws?.connect()
  }

  // -------------------- WebSocket错误 结束 --------------------

  // ---------------------------------------- 断线重连 结束 ----------------------------------------

  // ---------------------------------------- 客户状态记录 开始 ----------------------------------------

  // 人工直呼，应该在电话拨通后绑定并更新，挂断电话后解绑
  // 人机协同，应该在来电弹窗时绑定并更新，坐席漏接、坐席挂断、客户挂断后解绑

  // 客户状态记录 页面展示
  const eventList = ref<EventInfo[]>([])
  // 客户状态记录 接口数据
  const eventRes = ref<EventInfo[]>([])
  // 坐席绑定的电话号码
  let seatBindPhone: string = ''

  /**
   * 重置客户状态记录列表
   */
  const resetEventList = () => {
    eventList.value = []
  }
  /**
   * 客户状态记录，格式化列表
   * 将接口数据转换成页面展示
   */
  const formatEventList = () => {
    if (!eventRes.value?.length) {
      // 当没有数据时，清空eventList以触发响应式更新
      eventList.value.splice(0, eventList.value.length)
      return
    }

    // 格式化后的结果列表
    let list: EventInfo[] = []

    // 展示产品名称，目前采用的方法：一个字段里，字符串用竖线“|”分隔，前面是info_key，后面是产品名称

    // 遍历列表，判断当前有效产品和之前有效产品是否不同：
    // 不同，则当前项展示新的产品；
    // 相同，则当前项不展示任何产品；
    // 特别地，
    // 第一项有产品，始终显示产品；
    // 当前项是未知产品，不展示任何产品。

    let lastValidProduct: EventInfo = {}

    for (let i = 0; i < eventRes.value.length; i++) {
      // 深拷贝当前项
      const currentItem: EventInfo = JSON.parse(JSON.stringify(eventRes.value.at(i) ?? {}))

      // 拆分infoKey和名称
      const currentArr = (currentItem?.product ?? '').split('|') ?? []
      currentItem.productInfoKey = currentArr.at(0)?.trim() ?? ''
      currentItem.productDisplayName = currentArr.at(1)?.trim() ?? ''

      if (!currentItem.productDisplayName || currentItem.productDisplayName?.includes('产品名未知')) {
        // 产品名为空或者未知就不展示
        currentItem.productDisplayName = ''
      } else {
        // 产品名有效
        if (i === 0 || currentItem.productInfoKey !== lastValidProduct.productInfoKey) {
          // 第一项或者前后产品不同，展示
          // 更新最新展示的有效产品
          lastValidProduct = JSON.parse(JSON.stringify(currentItem))
        } else {
          // 前后产品相同，不展示
          currentItem.productDisplayName = ''
        }
      }

      list.push(currentItem)
    }

    // 产品信息整理完了，按时间正序排序
    list = list.sort((a: EventInfo, b: EventInfo) => {
      return dayjs(a?.occurrenceTime ?? '').valueOf() - dayjs(b?.occurrenceTime ?? '').valueOf()
    })

    // 全部处理完毕，更新DOM
    // 使用splice确保响应式更新
    eventList.value.splice(0, eventList.value.length, ...list)
  }
  /**
   * 客户状态记录 被动更新 增量更新
   * @param {EventInfo} data ws服务器主动发来的客户状态记录
   */
  const handleReceiveEvent = (data?: EventInfo) => {
    if (data) {
      // 增量更新
      eventRes.value.push(data)
      report({
        type: SeatLogTypeEnum['详细'],
        action: SeatLogActionEnum['客户状态记录-增量更新'],
        desc: '被动更新 增量更新 记录数量 ' + (eventRes.value?.length ?? 0),
        phone: seatBindPhone,
      })
      // 格式化列表
      formatEventList()

      // 强制触发响应式更新（防止某些情况下Vue无法检测到变化）
      nextTick(() => {
        // 通过修改数组长度再恢复来强制触发响应式
        const currentLength = eventList.value.length
        if (currentLength > 0) {
          eventList.value.push({} as EventInfo)
          eventList.value.pop()
        }
      })
    }
  }
  /**
   * 客户状态记录 主动更新 全量更新
   *
   * 一般是在通话抽屉弹出，并且成功请求接口后，执行一次
   * 区别于websocket服务器推送到浏览器的增量更新，这里是用于初始化时的全量更新
   * @param {string} phone 电话号码
   */
  const getEventList = async (phone: string) => {
    // 如果还在通话页，就尝试更新；如果在线索页，那就不更新了
    if (seatPage.value !== SeatPageEnum.PHONE) {
      return
    }

    // console.log('callRecordForHumanMachine.value', JSON.parse(JSON.stringify(callRecordForHumanMachine.value)))
    const params: EventInfoParams = {
      phone: phone ?? '',
      startTime: callRecordForHumanMachine.value.callOutTime ?? dayjs().format('YYYY-MM-DD HH:mm:ss') ?? '',
    }
    const [err, res] = <[any, EventInfo[]]>await to(seatWorkbenchCallModel.getEventList(params))
    if (err) {
      ElMessage({
        type: 'warning',
        message: '无法正确获取客户状态记录',
        duration: 1000,
      })
      console.error('客户状态记录', '无法正确获取客户状态记录')
      report({
        type: SeatLogTypeEnum['错误'],
        action: SeatLogActionEnum['客户状态记录-全量更新'],
        desc: '主动更新 全量更新 接口错误：' + err.toString(),
        phone: seatBindPhone,
        params,
      })
      return
    }
    // 全量更新
    eventRes.value = res?.length ? [...res] : []
    report({
      type: SeatLogTypeEnum['详细'],
      action: SeatLogActionEnum['客户状态记录-全量更新'],
      desc: '主动更新 全量更新 记录数量 ' + (eventRes.value?.length ?? 0),
      phone: seatBindPhone,
      params,
    })
    // 格式化列表
    formatEventList()
  }
  /**
   * 客户状态记录 坐席绑定当前通话
   *
   * 一般是在弹窗时，并且成功请求接口后，执行一次，
   * 服务器的websocket连接池是按坐席账号进行索引查找的，
   * 所以只有坐席和当前通话绑定，服务器才可以将此通电话通过对应坐席的websocket连接发到浏览器
   *
   * @param {string} phone 绑定的电话号码
   */
  const bindSeatAndPhone = async (phone: string = '') => {
    // 号码为空，不能绑定
    if (!phone) {
      console.warn('号码为空，不能绑定')
      report({
        type: SeatLogTypeEnum['警告'],
        action: SeatLogActionEnum['客户状态记录-绑定当前通话'],
        desc: '号码为空，不能绑定',
        phone: seatBindPhone,
      })
      return
    }

    try {
      const msg = {
        fsIp: fsAccount.ip,
        fsUser: fsAccount.account,
        callId: callInfo.value.callId,
        action: 'pop-up',
        phone: phone,
      }
      ws?.send(msg)
      seatBindPhone = msg.phone ?? ''
      report({
        type: SeatLogTypeEnum['详细'],
        action: SeatLogActionEnum['WebSocket请求'],
        desc: '坐席绑定当前通话',
        phone: seatBindPhone,
        params: msg || {},
      })
      report({
        type: SeatLogTypeEnum['详细'],
        action: SeatLogActionEnum['客户状态记录-绑定当前通话'],
        desc: '坐席绑定当前通话',
        phone: seatBindPhone,
      })
      await getEventList(phone)
    } catch (e) {
      report({
        type: SeatLogTypeEnum['错误'],
        action: SeatLogActionEnum['客户状态记录-绑定当前通话'],
        desc: '坐席无法绑定当前通话',
        phone: seatBindPhone,
      })
      ElMessage.error('无法正确获取客户状态记录：绑定出错')
      console.error('客户状态记录', '无法正确绑定电话号码')
    }
  }
  /**
   * 客户状态记录 坐席解绑当前通话
   *
   * 一般是在话后处理执行一次
   * 通话结束了，将坐席和此通电话解绑，以便坐席能够准备和下一通电话绑定
   */
  const unbindSeatAndPhone = async () => {
    // 不管在通话页还是线索页，如果有绑定的电话号码，都需要解绑，有始有终
    if (!seatBindPhone) {
      return
    }

    try {
      const msg = {
        fsIp: fsAccount.ip,
        fsUser: fsAccount.account,
        callId: callInfo.value.callId,
        action: 'pop-down',
        phone: seatBindPhone,
      }
      ws?.send(msg)
      report({
        type: SeatLogTypeEnum['详细'],
        action: SeatLogActionEnum['WebSocket请求'],
        desc: '坐席解绑当前通话',
        phone: seatBindPhone,
        params: msg || {},
      })
      report({
        type: SeatLogTypeEnum['详细'],
        action: SeatLogActionEnum['客户状态记录-解绑当前通话'],
        desc: '坐席解绑当前通话',
        phone: seatBindPhone,
      })
      seatBindPhone = ''
      // 清空客户状态记录
      eventRes.value.splice(0, eventRes.value.length)
      eventList.value.splice(0, eventList.value.length)
    } catch (e) {
      report({
        type: SeatLogTypeEnum['警告'],
        action: SeatLogActionEnum['客户状态记录-解绑当前通话'],
        desc: '坐席无法解绑当前通话',
        phone: seatBindPhone,
      })
      ElMessage.error('无法正确获取客户状态记录：解绑出错')
      console.error('客户状态记录', '无法正确解绑电话号码')
    }
  }

  // ---------------------------------------- 客户状态记录 结束 ----------------------------------------

  // ---------------------------------------- 坐席日志 开始 ----------------------------------------

  /**
   * 发送坐席日志，实际处理
   * @param {SeatLogItem} msg 消息内容
   */
  const reportSeatLog = async (msg: SeatLogItem) => {
    const content: SeatLogItem = {
      desc: msg.desc ?? undefined,
      phone: msg.phone ?? seatBindPhone ?? undefined,
      callType: msg.callType ?? callType.value ?? undefined,
      seatStatus: msg.seatStatus ?? seatStatus.value ?? undefined,
      taskCount: msg.taskCount ?? seatTaskList.value.length ?? undefined,
      remark: msg.remark ?? undefined,
      recordId: callRecordForManualDirect.value.recordId ?? callRecordForHumanMachine.value.recordId ?? undefined,
      callId: callInfo.value.callId ?? undefined,
      fsUser: fsAccount.account ?? undefined,
      fsIp: fsAccount.ip ?? undefined,
      params: msg.params ?? undefined,
      response: msg.response ?? undefined,
    }
    const params: SeatLogParam = {
      reportTime: msg?.reportTime ?? dayjs().format('YYYY-MM-DD HH:mm:ss'),
      type: msg?.type ?? SeatLogTypeEnum['信息'],
      account: msg?.account ?? currentSeat.value.account ?? userStore.account ?? '',
      title: msg?.action ?? '',
      content: JSON.stringify(content),
    }
    await seatLogModel.log(params).then(() => {
      console.debug('******', '坐席日志发送成功', params.title, JSON.parse(JSON.stringify(params)))
    }).catch(() => {
      console.warn('******', '坐席日志发送失败', params.title, JSON.parse(JSON.stringify(params)))
    })
    trace({
      page: (msg?.action || '') + (msg?.desc ? ('，' + msg?.desc || '') : ''),
      params: content,
    })
  }
  /**
   * 发送坐席日志，异步，不阻塞执行
   * @param {SeatLogItem} msg 消息内容
   */
  const report = (msg: SeatLogItem) => {
    reportSeatLog(msg).then(() => {
    }).catch(() => {
    })
  }
  /**
   * 发送坐席日志，同步，阻塞执行
   * @param {SeatLogItem} msg 消息内容
   */
  const reportSync = async (msg: SeatLogItem) => {
    await reportSeatLog(msg)
  }

  // ---------------------------------------- 坐席日志 结束 ----------------------------------------

  // ---------------------------------------- 空闲时长 开始 ----------------------------------------

  // 空闲时长，定时器
  let idleTimer: number | null = null
  // 空闲时长，当前秒数
  const idleSecond = ref(0)
  // 空闲时长，开始日期时间
  let idleStartDatetime = dayjs()

  /**
   * 空闲时长，重置定时器
   */
  const resetIdleTimer = () => {
    idleSecond.value = 0
  }
  /**
   * 空闲时长，处理定时器
   */
  const handleIdleTimer = () => {
    idleSecond.value = dayjs().diff(idleStartDatetime, 'second') ?? 0
  }
  /**
   * 空闲时长，关闭定时器
   */
  const closeIdleTimer = () => {
    if (typeof idleTimer === 'number') {
      clearInterval(idleTimer)
    }
    idleTimer = null
  }
  /**
   * 空闲时长，开启定时器
   */
  const openIdleTimer = () => {
    closeIdleTimer()
    resetIdleTimer()
    idleStartDatetime = dayjs()
    idleTimer = <number><unknown>setInterval(handleIdleTimer, 1000)
  }

  // ---------------------------------------- 空闲时长 结束 ----------------------------------------

  // ---------------------------------------- 通话携带信息 开始 ----------------------------------------

  // 通话携带信息
  const callCarryInfo = ref<CallCarryInfo>({
    name: '',
    age: null,
  })
  /**
   * 重置通话携带信息
   */
  const resetCallCarryInfo = () => {
    callCarryInfo.value.name = ''
    callCarryInfo.value.age = null
  }
  /**
   * 更新通话携带信息
   * @param {CallCarryInfo} data 新数据
   */
  const updateCallCarryInfo = (data: CallCarryInfo) => {
    if (data?.name) {
      callCarryInfo.value.name = data?.name
      console.log('更新通话携带信息', 'name', callCarryInfo.value.name)
    }
    if (typeof data?.age === 'number') {
      callCarryInfo.value.age = data?.age
      console.log('更新通话携带信息', 'age', callCarryInfo.value.age)
    }
  }
  /**
   * 主动查询通话携带信息
   */
  const getCallCarryInfo = async () => {
    const recordId = (callType.value === SeatCallTypeEnum.DIRECT
      ? callRecordForManualDirect.value.recordId
      : callRecordForHumanMachine.value.recordId)
      ?? undefined
    const params = {
      isHumanDirect: callType.value === SeatCallTypeEnum.DIRECT,
      // 移除前缀
      recordId: removeStrPrefix(['C1_', 'C2_', 'C3_'])(recordId),
    }
    const [err, res] = <[any, CallCarryInfo]>await to(seatWorkbenchCallModel.getCallCarryInfo(params))
    if (err) {
      console.error('无法查询通话携带信息')
      return
    }
    console.log('CallCarryInfo res', JSON.parse(JSON.stringify(res)))
    resetCallCarryInfo()
    updateCallCarryInfo(res)
  }

  // ---------------------------------------- 通话携带信息 结束 ----------------------------------------

  // ---------------------------------------- 重新发送短信 开始 ----------------------------------------

  // 重新发送短信 正在提交
  const loadingResendSms = ref<boolean>(false)
  // 重新发送短信 提交节流锁
  const throttleResendSms = new Throttle(loadingResendSms)

  /**
   * 重新发送短信的具体逻辑
   * @param {boolean} fromAuto 是否来自自动发送
   */
  const resendSms = async (fromAuto: boolean = false) => {
    // 节流锁上锁
    if (throttleResendSms.check()) {
      return
    }
    throttleResendSms.lock()

    // 发送短信业务逻辑
    // 对当前坐席和当前线索综合匹配对应的短信模板并进行校验，合规后发送短信

    // 1. 根据当前坐席账号所属主账号，查找所有短信模板
    // 处理参数
    const smsTemplateParams: SmsTemplateParams = {
      groupId: userStore.groupId ?? '',
    }
    // 请求接口
    const [smsTemplateError, smsTemplateRes] = <[any, MerchantSmsTemplateItem[]]>await to(merchantSmsTemplateModel.getSmsTemplate(smsTemplateParams))
    // 返回失败结果
    if (smsTemplateError) {
      const errStr = fromAuto ? '自动重发备注短信失败：无法获取短信模板列表' : '无法获取短信模板列表，请重试'
      ElMessage.error(errStr)
      report({
        type: SeatLogTypeEnum['错误'],
        action: SeatLogActionEnum['自动发送备注短信'],
        desc: errStr,
        params: smsTemplateParams,
        response: smsTemplateError.toString(),
      })
      // 节流锁解锁
      throttleResendSms.unlock()
      return
    }
    // 返回成功结果
    // 更新全部列表
    const smsTemplateAllList: MerchantSmsTemplateItem[] = smsTemplateRes?.length ? smsTemplateRes : []

    // 2. 按线索备注里提供的【】括号内的短信签名，从全部列表里找出对应的短信模板
    // 线索备注按【】分割
    const clueCommentList = (currentClue.value.comment ?? '').trim().split(/[【】]/) || []
    // 无论什么情况，短信签名都在数组索引1里，短信内容都在数组索引2里
    const messageSign = clueCommentList.at(1) ?? ''
    const messageContent = clueCommentList.at(2) ?? ''
    // 从全部列表里找出对应的短信模板，并且短信模板名称不为空
    const smsTemplateItem: MerchantSmsTemplateItem | undefined = smsTemplateAllList.find((item: MerchantSmsTemplateItem) => {
      return item.messageSign === messageSign && !!item.messageSign && !!messageSign
    })
    // 如果没有找到对应的短信模板，返回错误提示
    if (!smsTemplateItem) {
      const errStr = fromAuto ? '自动重发备注短信失败：未找到短信模板' : '未找到短信模板，请联系管理员'
      ElMessage.warning(errStr)
      report({
        type: SeatLogTypeEnum['错误'],
        action: SeatLogActionEnum['自动发送备注短信'],
        desc: errStr,
      })
      // 节流锁解锁
      throttleResendSms.unlock()
      return
    }

    // 记录错误信息，最后一块报错
    let problem: string[] = []

    // 3. 将线索备注的短信内容提取出来，放入发送短信参数的自定义变量的备注里
    // 无论什么情况，短信内容都在数组索引2里
    // 判断短信全文长度，短信签名【】括号本身算2个+短信签名+短信内容，不超过69
    if (2 + messageSign.length + messageContent.length > 69) {
      problem.push(fromAuto ? '自动重发备注短信失败：短信内容超长' : '短信内容超长，请联系管理员')
    }

    // 4. 校验短信模板启用状态
    if (smsTemplateItem.templateStatus === SmsTemplateStatusEnum['禁用']) {
      problem.push(fromAuto ? '自动重发备注短信失败：短信通道异常' : '短信通道异常')
    }

    // 5. 校验短信模板的自定义变量，应该只含有一个变量“备注”，不包含其他变量
    if (smsTemplateItem.variableUsed?.length === 1 && smsTemplateItem.variableUsed.at(0)?.variableName === 'SMS') {
      // 只含有一个变量“备注”
    } else {
      // 没有自定义变量
      problem.push(fromAuto ? '自动重发备注短信失败：变量值缺失' : '变量值缺失')
    }

    // 一块报错
    if (problem.length) {
      problem.forEach((errStr: string) => {
        ElMessage.warning(errStr)
        report({
          type: SeatLogTypeEnum['错误'],
          action: SeatLogActionEnum['自动发送备注短信'],
          desc: errStr,
        })
      })
      // 节流锁解锁
      throttleResendSms.unlock()
      return
    }

    // 准备好自定义变量“备注”
    const customVariableItem: VariableSmsPojoItem = <VariableSmsPojoItem>smsTemplateItem.variableUsed![0]

    // 6. 至此校验通过，下面开始发送短信
    // 处理参数
    const smsSendParam: SmsSendParam = {
      clueId: currentClue.value.id ?? undefined,
      smsTemplateId: smsTemplateItem.id ?? undefined,
      variableSmsPojoList: [
        {
          variableName: customVariableItem.variableName,
          variableValue: messageContent,
        },
      ],
      callSeatId: currentSeat.value.id ?? undefined,
      callTeamId: currentSeat.value.callTeamId ?? undefined,
    }
    // 请求接口
    let sendSmsErr: any, sendSmsRes: any
    if (callType.value === SeatCallTypeEnum.DIRECT) {
      // 人工直呼
      ([sendSmsErr, sendSmsRes] = await to(smsSendModel.sendSmsInDirectCall(smsSendParam)))
    } else {
      // 人机协同
      ([sendSmsErr, sendSmsRes] = await to(smsSendModel.sendSmsInHumanMachine(smsSendParam)))
    }
    // 返回失败结果
    if (sendSmsErr) {
      const errStr = fromAuto ? '自动重发备注短信失败：发送接口错误' : '发送失败'
      ElMessage.warning(errStr)
      report({
        type: SeatLogTypeEnum['错误'],
        action: SeatLogActionEnum['自动发送备注短信'],
        desc: errStr,
        params: smsSendParam,
        response: sendSmsErr.toString(),
      })
      // 节流锁解锁
      throttleResendSms.unlock()
      return
    }
    // 返回成功结果
    // 判断是否包含违禁词
    if (sendSmsRes?.length) {
      const errStr = fromAuto
        ? '自动重发备注短信失败：' + `变量【${sendSmsRes.at(0)}】存在违禁词【${sendSmsRes.at(1)}】`
        : `变量【${sendSmsRes.at(0)}】存在违禁词【${sendSmsRes.at(1)}】`
      ElMessage({
        message: errStr,
        type: 'warning',
        duration: 5000,
      })
      report({
        type: SeatLogTypeEnum['错误'],
        action: SeatLogActionEnum['自动发送备注短信'],
        desc: errStr,
      })
    } else {
      const successStr = fromAuto ? '自动重发备注短信成功' : '已重新发送'
      ElMessage.success(fromAuto ? '自动重发备注短信成功' : '已重新发送')
      report({
        type: SeatLogTypeEnum['详细'],
        action: SeatLogActionEnum['自动发送备注短信'],
        desc: successStr,
      })
    }
    // 节流锁解锁
    throttleResendSms.unlock()
  }

  // ---------------------------------------- 重新发送短信 结束 ----------------------------------------

  // ---------------------------------------- 人机协同接管 开始 ----------------------------------------

  // 接管状态管理
  const takeoverState = ref({
    isWaitingForIce: false,
    iceWaitTimer: null as number | null,
    maxWaitTime: 10000,
    dtmfRetryCount: 0,
    maxDtmfRetries: 3
  })

  /**
   * 处理人机协同接管
   * 等待ICE连接成功后再发送DTMF
   */
  const handleTakeover = async (): Promise<void> => {
    console.log('开始人机协同接管流程')

    // 重置状态
    takeoverState.value.isWaitingForIce = false
    takeoverState.value.dtmfRetryCount = 0

    // 清理之前的定时器
    if (takeoverState.value.iceWaitTimer) {
      clearTimeout(takeoverState.value.iceWaitTimer)
      takeoverState.value.iceWaitTimer = null
    }

    try {
      // 检查当前ICE状态
      if (iceState.value === 'connected' || iceState.value === 'completed') {
        console.log('ICE已连接，直接发送DTMF')
        await sendTakeoverDTMF()
      } else {
        console.log('ICE未连接，等待连接成功，当前状态：', iceState.value)
        await waitForIceConnection()
      }
    } catch (error) {
      console.error('接管流程出错：', error)
      const errMsg = (error as ResponseData).msg || (error as Error).message || error as string || ''
      // await handleTakeoverFailure('接管流程出错：' + errMsg)
      report({
        type: SeatLogTypeEnum['错误'],
        action: SeatLogActionEnum['人机协同-接管'],
        desc: '人机协同接管 接管流程出错：' + errMsg,
      })
    }
  }

  /**
   * 等待ICE连接成功
   */
  const waitForIceConnection = (): Promise<void> => {
    return new Promise((resolve, reject) => {
      takeoverState.value.isWaitingForIce = true

      // 显示等待提示
      ElMessage({
        message: '正在建立音频连接，请稍候',
        type: 'warning',
        duration: 2000
      })

      // 监听ICE状态变化
      const stopWatcher = watch(iceState, async (newState) => {
        console.log('等待ICE连接，状态变化', newState)

        stopWatcher()
        clearTimeout(takeoverState.value.iceWaitTimer!)
        takeoverState.value.isWaitingForIce = false

        if (newState === 'connected' || newState === 'completed') {
          // ICE连接成功
          console.log('ICE连接成功，发送DTMF')
          try {
            await sendTakeoverDTMF()
            resolve()
          } catch (error) {
            reject(error)
          }
        } else if (newState === 'failed' || newState === 'disconnected') {
          // ICE连接失败
          console.error('ICE连接失败，状态：', newState)
          reject(new Error('ICE连接失败'))
        }
      })

      // 设置超时
      takeoverState.value.iceWaitTimer = window.setTimeout(() => {
        console.error('等待ICE连接超时')
        stopWatcher()
        takeoverState.value.isWaitingForIce = false
        reject(new Error('音频连接超时'))
      }, takeoverState.value.maxWaitTime)
    })
  }

  /**
   * 发送接管DTMF信号
   */
  const sendTakeoverDTMF = (): Promise<void> => {
    return new Promise((resolve, reject) => {
      const sipHandle = sipCallList[instanceId]

      if (!sipHandle) {
        reject(new Error('SIP通话句柄不可用'))
        report({
          type: SeatLogTypeEnum['错误'],
          action: SeatLogActionEnum['人机协同-接管'],
          desc: '人机协同接管 SIP通话句柄不可用',
        })
        return
      }

      console.log('发送接管DTMF信号，重试次数：', takeoverState.value.dtmfRetryCount)

      /**
       * 内部函数，重试方法
       */
      const retry = () => {
        if (takeoverState.value.dtmfRetryCount < takeoverState.value.maxDtmfRetries) {
          takeoverState.value.dtmfRetryCount++
          console.log('DTMF发送失败，进行重试，重试次数：', takeoverState.value.dtmfRetryCount)
          report({
            type: SeatLogTypeEnum['错误'],
            action: SeatLogActionEnum['人机协同-接管'],
            desc: '人机协同接管 DTMF发送失败，进行重试，重试次数：' + takeoverState.value.dtmfRetryCount,
          })

          setTimeout(async () => {
            try {
              await sendTakeoverDTMF()
              resolve()
            } catch (retryError) {
              reject(retryError)
            }
          }, 1000)
        } else {
          report({
            type: SeatLogTypeEnum['错误'],
            action: SeatLogActionEnum['人机协同-接管'],
            desc: '人机协同接管 DTMF发送失败，重试次数已用完，重试次数：' + takeoverState.value.dtmfRetryCount,
          })
          handleTakeoverFailure('DTMF发送失败，重试次数已用完')
          reject(new Error('DTMF发送失败，重试次数已用完'))
        }
      }

      // 设置DTMF发送超时
      const dtmfTimeout = setTimeout(() => {
        console.error('DTMF发送超时')
        report({
          type: SeatLogTypeEnum['错误'],
          action: SeatLogActionEnum['人机协同-接管'],
          desc: '人机协同接管 DTMF发送超时',
        })
        reject(new Error('DTMF发送超时'))
      }, 10000)

      setTimeout(() => {
        try {
          sipHandle.dtmf({
            dtmf: { tones: '**' },
            success: async (data: any) => {
              clearTimeout(dtmfTimeout)
              console.log('DTMF发送成功：', data)

              ElMessage({
                message: '接管信号发送成功',
                type: 'success',
                duration: 2000
              })

              report({
                type: SeatLogTypeEnum['信息'],
                action: SeatLogActionEnum['人机协同-接管'],
                desc: '人机协同接管 成功发送DTMF',
              })

              // 调用接管接口
              try {
                await callTakeoverAPI()
                resolve()
              } catch (apiError) {
                reject(apiError)
              }
            },
            error: async (error: string) => {
              clearTimeout(dtmfTimeout)
              console.error('DTMF发送失败', error)
              report({
                type: SeatLogTypeEnum['错误'],
                action: SeatLogActionEnum['人机协同-接管'],
                desc: '人机协同接管 DTMF发送失败，错误原因：' + error,
              })
              retry()
            }
          } as PluginDtmfParam)
        } catch (error) {
          clearTimeout(dtmfTimeout)
          console.error('DTMF发送失败：', error)
          report({
            type: SeatLogTypeEnum['错误'],
            action: SeatLogActionEnum['人机协同-接管'],
            desc: '人机协同接管 DTMF发送失败：' + error,
          })
          retry()
          reject(new Error('DTMF发送失败：' + (error as Error).message))
        }
      }, 1000)
    })
  }

  /**
   * 调用接管接口
   */
  const callTakeoverAPI = async (): Promise<void> => {
    try {
      const res = <SeatCallMixInterveneRes>await seatWorkbenchCallModel.startSpeakHumanMachine({
        recordId: callRecordForHumanMachine.value?.recordId,
        isTransToHuman: true
      })
      console.log('人机协同 接管（无监听直接介入）', JSON.parse(JSON.stringify(res)))
      // 开始接待计时
      openReceptionTimer()
      // 更新线索
      updateCurrentClue({
        id: res.clueId ?? -1,
        callSeatId: res.callSeatId ?? -1,
      })
      console.log('人机协同 接管 更新线索信息', JSON.parse(JSON.stringify(currentClue.value)))
      // 更新表单ID
      formId.value = res?.formId ?? null
      // 更新坐席状态
      seatInfoStore.updateSeatStatus(SeatStatusEnum.HUMAN_MACHINE_DIALING)
      console.log('接管接口调用成功')
      report({
        type: SeatLogTypeEnum['信息'],
        action: SeatLogActionEnum['人机协同-接管'],
        desc: '人机协同接管接口调用成功',
        phone: callRecordForHumanMachine.value.phone,
      })
    } catch (error) {
      console.error('接管接口调用失败:', error)
      const errMsg = (error as ResponseData).msg || (error as Error).message || error as string || ''
      throw new Error('接管接口调用失败：' + errMsg)
    }
  }

  /**
   * 处理接管失败
   */
  const handleTakeoverFailure = async (reason: string): Promise<void> => {
    console.error('人机协同接管失败:', reason)

    // 记录错误日志
    report({
      type: SeatLogTypeEnum['错误'],
      action: SeatLogActionEnum['人机协同-接管'],
      desc: '人机协同接管失败：' + reason,
      phone: callRecordForHumanMachine.value.phone,
    })

    // 显示失败原因弹窗
    showTakeoverFailureDialog(reason)
  }

  /**
   * 显示接管失败弹窗
   */
  const showTakeoverFailureDialog = (reason: string): void => {
    ElMessageBox.alert(
      `接管失败原因：${reason}`,
      '人机协同接管失败',
      {
        confirmButtonText: '确定',
        type: 'error',
        callback: () => {
          // 显示退出接听弹窗
          dialogExitMonitorVisible.value = true
        }
      }
    )
  }

  /**
   * 清理接管状态
   */
  const cleanupTakeover = (): void => {
    if (takeoverState.value.iceWaitTimer) {
      clearTimeout(takeoverState.value.iceWaitTimer)
      takeoverState.value.iceWaitTimer = null
    }
    takeoverState.value.isWaitingForIce = false
    takeoverState.value.dtmfRetryCount = 0
  }

  // ---------------------------------------- 人机协同接管 结束 ----------------------------------------

  return {
    currentClue,
    clueList,
    lastClue,
    needUpdateClueList,
    updateCurrentClue,
    clearClueHistory,

    callRecordForManualDirect,
    callRecordForHumanMachine,
    phone,

    resetWorkbench,
    checkOnline,
    checkStatus,
    checkTask,

    instanceId,

    fsAccount,
    sipCallAction,
    sipCallActionParams,
    remoteAudioDomList,

    registered,
    busy,
    iceState,

    createJanus,
    destroyJanus,
    updateSeatInfo,
    online,
    register,
    requestOffline,
    offline,
    forceOffline,
    intervene,
    hangup,
    startHeartbeatTimer,
    stopHeartbeatTimer,
    checkJanus,
    checkRegistered,
    checkBusy,

    callType,
    callInfo,
    callingSecond,
    followUpLogId,
    formId,
    humanMachineSpeechCraftId,
    launchCall,
    callNext,
    updateCallType,

    dialogCallingVisible,
    startCallingTimer,
    stopCallingTimer,
    cancelCall,

    dialogExitMonitorVisible,

    loadingIntervene,

    dialogCallFailVisible,
    needCallNext,
    onClickCancelCall,
    onClickRetryCall,
    onClickCallNext,
    callFailReason,
    callNextCountdownSecond,
    showDialogCallFail,

    showIncomingCall,
    hideIncomingCall,
    startWaitingTimer,
    stopWaitingTimer,
    handleAccept,
    switchLocalAudioMuted,
    checkOutTaskNotificationRef,
    linkedTaskStartNotificationRef,

    receptionSecond,
    openReceptionTimer,
    fastForwardReceptionTimer,

    notifyCheckOut,
    notifyTaskStart,
    needUpdateWorkbenchStatistics,

    dialogOfflineVisible,
    clearCallInfo,

    initDialAudio,

    notifyNewClue,
    closeNotifyNewClue,

    eventList,
    resetEventList,
    handleReceiveEvent,
    unbindSeatAndPhone,

    report,

    idleSecond,
    resetIdleTimer,
    closeIdleTimer,
    openIdleTimer,

    callCarryInfo,
    updateCallCarryInfo,
    getCallCarryInfo,

    loadingResendSms,
    resendSms,
  }
})
