<template>
  <!--模块标题-->
  <HeaderBox title="供应线路网关" />
  <div class="tw-p-[12px] tw-h-[calc(100vh-70px)] tw-min-w-[1080px] tw-overflow-y-hidden tw-flex tw-flex-col">
    <div class="tw-flex tw-justify-end tw-mb-[12px]">
      <el-button
        size="default"
        type="primary"
        @click="editLeftItem()"
      >
        创建供应线路网关
      </el-button>
    </div>
    <div class="tw-w-full tw-p-[16px] tw-bg-white tw-mb-[16px] tw-flex tw-items-center">
      <el-input
        style="width: 300px;"
        v-model.trim="leftSearchForm.name"
        placeholder="搜索供应线路网关"
        clearable
        @keyup.enter="searchAction"
      >
      </el-input>
      <el-button class="tw-ml-[12px]" type="primary" @click="searchAction" link>
        <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="filter" color="none" /></el-icon>
        <span>查询</span>
      </el-button>
    </div>
    <el-table
      :data="tableTemp"
      v-loading="loading"
      ref="tableRef"
      row-key="id"
      class="tw-grow"
      :header-cell-style="tableHeaderStyle"
      stripe
      @sort-change="handleSortChange"
    >
      <el-table-column property="name" label="网关名称" fixed="left" align="left" width="240" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
      <el-table-column property="gatewayNumber" label="网关编号" align="left" width="160" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="concurrentCount" label="并发(实时/上限)" sortable align="left" :formatter="formatterEmptyData" width="200">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ `${row.concurrentCount ?? '-'} / ${row.concurrentLimit ?? '-'}` }}
        </template>
      </el-table-column>
      <el-table-column property="callingRestrictions" label="频率限制" align="left" min-width="200" show-overflow-tooltip>
        <template #default="{ row }">
          {{ translateCallingRestrictions(row) + translateDialingRestrictions(row) || '-' }}
        </template>
      </el-table-column>
      <el-table-column property="notes" label="运营备注" align="left" :formatter="formatterEmptyData" min-width="160" show-overflow-tooltip></el-table-column>
      <el-table-column property="createTime" label="创建时间" sortable align="center" :formatter="formatterEmptyData" width="160">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-center">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ row.createTime ? dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="160" align="right" fixed="right">
        <template #default="{ row }">
          <div class="table-btn-box">
            <el-button type="primary" link @click="editLeftItem(row)">编辑</el-button>
            <el-button type="primary" link @click="checkConcurrent(row)">并发趋势</el-button>
            <el-button type="danger" link @click="delLeftItem(row)">删除</el-button>
          </div>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
      </template>
    </el-table>
    <PaginationBox
      :pageSize="pageSize"
      :pageSizeList="pageLeftSizeList"
      :currentPage="currentPage"
      :total="tableData?.length||0"
      @search="searchAction()"
      @update="updateLeftList"
    >
    </PaginationBox>
  </div>
  <el-drawer
    v-model="drawerVisible"
    :size="getDrawerWidth"
    :with-header="true"
  >
    <template v-slot:header>
      <div class="tw-bg-white tw-h-[50px]  tw-flex tw-items-end tw-pb-[12px]">
        <div class="tw-font-semibold tw-text-left tw-text-[var(--primary-black-color-600)]">
          【{{currentEditItem.name || '-'}}】并发趋势
        </div>
        <el-tooltip content="刷新" placement="right" :show-after="500">
          <el-icon size="13" color="var(--el-color-primary)" class="tw-cursor-pointer tw-ml-0.5 tw-pb-1" @click="needRefresh=true">
            <SvgIcon name="reset" color="inherit"/>
          </el-icon>
        </el-tooltip>
      </div>
    </template>
    <el-scrollbar wrap-class="tw-p-[12px] tw-bg-[#f2f3f5]">
      <LineConcurrentBox :lineInfo="currentEditItem.gatewayNumber!" v-model:refresh="needRefresh"></LineConcurrentBox>
    </el-scrollbar>
  </el-drawer>
  <GatewayEditDialog
    v-model:visible="createLeftVisible"
    :editData="currentEditItem"
    @confirm="searchAction"
  />
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onUnmounted, defineAsyncComponent } from 'vue'
import dayjs from 'dayjs'
import { CaretTop, CaretBottom } from '@element-plus/icons-vue'
import Confirm from '@/components/message-box'
import { GatewayItem, LineInfo } from '@/type/gateway'
import { gatewayModel } from '@/api/gateway'
import HeaderBox from '@/components/HeaderBox.vue'
import PaginationBox from '@/components/PaginationBox.vue'
import { ElMessage } from 'element-plus'
import { onBeforeRouteLeave } from 'vue-router'
import LineConcurrentBox from '@/views/operator/line-manager/components/LineConcurrentBox.vue'
import { tableHeaderStyle } from '@/assets/js/constant'
import { formatterEmptyData, handleTableSort } from '@/utils/utils'
import to from 'await-to-js'
import { trace } from '@/utils/trace'

// 用户权限获取
const GatewayEditDialog = defineAsyncComponent({ loader:() => { return import('./GatewayEditDialog.vue') }})

// loading信息
const loading = ref(false)

/**
 * 左侧模块
 */
class GatewayOrigin {
  id = undefined
  gatewayNumber = undefined
  supplyLineNumbers = []
  caps = undefined
  name = ''
  concurrentLimit = undefined
  callingRestrictions = []
  dialingRestrictions = []
  createTime = undefined
  notes = ''
}
// 左侧搜索内容
const leftSearchForm = reactive({
  name: ''
})
// 当前选中左侧元素
const currentItem = reactive<GatewayItem>(new GatewayOrigin())
// 左侧列表
const prop = ref('')
const order = ref('')
const tableData = ref<GatewayItem[] | null>([])
  const tableTemp = computed(() => {
  const data = handleTableSort(tableData.value || [], order.value, prop.value)
  return (data||[]).slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
})
const handleSortChange = (params: { prop: string, order: string }) => {
  prop.value = params.prop
  order.value = params.order
}
// 左侧分页数据
const pageLeftSizeList = [100, 200]
const pageSize = ref(pageLeftSizeList[0]!)
const currentPage = ref(1)
// 搜索左侧网关
const searchAction = async (id?: number) => {
  loading.value = true
  const res = await gatewayModel.getGatewayList() || []
  tableData.value = res.filter(item => !item.name || item.name?.includes(leftSearchForm.name)).sort((a, b) => dayjs(a.createTime).isAfter(b.createTime) ? -1 : 1)
  loading.value = false
}
const updateLeftList = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
  searchAction()
}
// 左侧创建编辑-网关
const createLeftVisible = ref(false)
const currentEditItem = reactive<GatewayItem>(new GatewayOrigin())
const editLeftItem = (item?: GatewayItem) => {
  if (item) {
    createLeftVisible.value = true
    Object.assign(currentEditItem, item || new GatewayOrigin())
  } else {
    createLeftVisible.value = true
    Object.assign(currentEditItem, new GatewayOrigin())
  }
}
// 左侧-删除网关，有线路组成时需要清空线路才能删除
const delLeftItem = async (row: GatewayItem) => {
  if (row.supplyLineNumbers && row.supplyLineNumbers.length > 0) {
    return ElMessage({
      type: 'warning',
      message: '请先清空网关内的供应线路!'
    })
  }
  Confirm({
    text: `您确定要删除网关【${row?.name||''}】?`,
    type: 'danger',
    title: `网关删除确认`,
    confirmText: '删除'
  }).then(async () => {
    loading.value = true
    trace({
      page: '供应线路网关-删除网关',
      params: {lineGatewayId: row.id!}
    })
    const [err] = await to(gatewayModel.deleteGateway({lineGatewayId: row.id!}))
    if (!err) ElMessage.success('网关删除成功')
    searchAction()
  }).catch(() => {}).finally(() => {
    loading.value = false
  })
}

const drawerVisible = ref(false)
const needRefresh = ref(false)
const getDrawerWidth = computed(() => {
  return window.innerWidth > 1400 ? '75%' : '950px'
})
// 查看并发趋势
const checkConcurrent= (item: GatewayItem) => {
  if (item && item?.gatewayNumber) {
    drawerVisible.value = true
    needRefresh.value = true
    Object.assign(currentEditItem, item || new GatewayOrigin())
  }
}

// 翻译【频率限制】- 拨打
const translateCallingRestrictions = (row: GatewayItem) => {
  let str2 = ''
  if (row.callingRestrictions && row.callingRestrictions.length > 0) {
    row.callingRestrictions.map((item: string) => {
      const [times, hours] = item.split('-')
      str2 += `${times}次/${hours}小时;`
    })
    str2 = `${str2}`
  }
  return str2 ? '拨打限制：' + str2 : ''
}
// 翻译【频率限制】- 接通
const translateDialingRestrictions = (row: GatewayItem) => {
  let str1 = ''
  if (row.dialingRestrictions && row.dialingRestrictions.length > 0) {
    row.dialingRestrictions.map(item => {
      const [times, hours] = item.split('-')
      str1 += `${times}次/${hours}小时;`
    })
    str1 = `${str1}`
  }
  return str1 ? '接通限制：' + str1 : ''
}
// 右侧表格： 新增线路
/**
 * 线路组成新增编辑
 */
const availableLineList = ref<LineInfo[] | null>([])

// 初始化
const init = () => {
  searchAction()
}
init()

const clearAllData = () => {
  Object.assign(currentItem, new GatewayOrigin())
  Object.assign(currentEditItem, new GatewayOrigin())
  availableLineList.value = null
  tableData.value = null
}
onUnmounted(() => {
  clearAllData()
})
onBeforeRouteLeave(() => {
  clearAllData()
})
</script>

<style scoped lang="postcss">
.el-table {
  font-size: 13px;
  :deep(.caret-wrapper) {
    display: none;
  }
  :deep(.cell) {
    padding: 0 8px;
  }
}
.table-btn-box {
  display: flex;
  .el-button {
    width: 60px;
  }
}

</style>
