<template>
  <ClientInfo />

  <TabsBox
    v-model:active="humanMachineActiveTab"
    class="workbench-tab"
    :tabList="tabList"
    @update:active="onUpdateTab"
  />

  <div v-show="humanMachineActiveTab===WorkbenchTabEnum['AI通话记录']" class="block tw-p-[8px]">
    <el-scrollbar>
      <div class="info-block-title">
        AI通话记录
      </div>
      <p class="info-item">
        <span class="info-title">客户名称：</span>
        <span class="info-content">{{ aiRecord.name || '-' }}</span>
      </p>
      <p class="info-item">
        <span class="info-title">名单编号：</span>
        <span class="info-content">{{ aiRecord.id || '-' }}</span>
      </p>
      <p class="info-item">
        <span class="info-title">所属省市：</span>
        <template v-if="aiRecord.province||aiRecord.city">
          <span class="info-content">
            {{ (aiRecord.province + ' ' + aiRecord.city) || '-' }}
          </span>
        </template>
        <template v-else>
          -
        </template>
      </p>
      <p class="info-item">
        <span class="info-title">被叫号码：</span>
        <span class="info-content">{{ aiRecord.recordId || '-' }}</span>
      </p>
      <p class="info-item">
        <span class="info-title">任务名称：</span>
        <span class="info-content">{{ aiRecord.taskName || '-' }}</span>
      </p>
      <p class="info-item">
        <span class="info-title">执行话术：</span>
        <span class="info-content">{{ aiRecord.scriptName || '-' }}</span>
      </p>
      <p class="info-item">
        <span class="info-title">AI意向分类：</span>
        <span class="info-content">{{ aiRecord.intentionClass || '-' }}</span>
      </p>
      <p class="info-item">
        <span class="info-title">AI标签：</span>
        <span class="info-content">
          <template v-if="aiRecord.intentionLabels?.length">
            <el-tag
              v-for="(labelItem,labelIndex) in (aiRecord.intentionLabels.split(',')??[])"
              :key="labelIndex"
              class="list-item-tag"
            >
              {{ labelItem }}
            </el-tag>
          </template>
          <template v-else>
            -
          </template>
        </span>
      </p>
      <div class="info-block-title">
        人工通话记录
      </div>

      <p class="info-item">
        <span class="info-title">人工意向分类：</span>
        <span class="info-content">
          <el-select
            v-model="manualRecord.manualIntention"
            value-key="intentionType"
            clearable
            placement="top"
          >
            <el-option
              v-for="intentionItem in intentionAllList"
              :key="intentionItem.intentionType"
              :label="intentionItem.intentionType + ' - ' + intentionItem.intentionName"
              :value="intentionItem"
            />
          </el-select>
        </span>
      </p>
      <p class="info-item tw-flex tw-items-start tw-min-h-[50px]">
        <span class="info-title">人工标签：</span>
        <span class="info-content">
          <!--可选标签下拉菜单-->
          <el-dropdown trigger="click" :hide-on-click="false" placement="top-start">
            <el-button type="primary" link>
              新增标签
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <!--列表有数据-->
                <template v-if="labelLeftList.length">
                  <el-dropdown-item v-for="left in labelLeftList" :key="left.id" @click="addManualLabel(left)">
                    {{ left.labelName }}
                  </el-dropdown-item>
                </template>
                <!--列表无数据-->
                <template v-else>
                  <div class="tw-min-w-[50px] tw-py-[4px] tw-text-[13px] tw-text-center">
                    （空）
                  </div>
                </template>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <br>
          <!--当前已选标签列表-->
          <el-tag
            v-for="selected in manualRecord.manualLabels"
            :key="selected.id"
            closable
            class="list-item-tag"
            @close="deleteManualLabel(selected)"
          >
            {{ selected.labelName }}
          </el-tag>
        </span>
      </p>
    </el-scrollbar>
  </div>

  <div v-show="humanMachineActiveTab===WorkbenchTabEnum['客户状态记录']" class="block tw-flex tw-flex-col tw-justify-start tw-self-stretch tw-flex-nowrap">
    <ClientStatus />
  </div>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, ref, watch } from 'vue'
import { IntentionType, LabelItem } from '@/type/IntentionType'
import { storeToRefs } from 'pinia'
import { SeatCallParam, SeatCallResponse, SeatStatusEnum, WorkbenchTabEnum } from '@/type/seat'
import { useSeatPhoneStore } from '@/store/seat-phone'
import { seatWorkbenchCallModel } from '@/api/seat'
import { TaskCallRecordItem } from '@/type/task'
import ClientInfo from './components/ClientInfo.vue'
import ClientStatus from './components/ClientStatus.vue'
import { useSeatInfoStore } from '@/store/seat/seat-info'
import { useInteractionStore } from '@/store/seat/interaction'

// 动态引入组件
const TabsBox = defineAsyncComponent(() => import('@/components/TabsBox.vue'))

// ---------------------------------------- 通用 开始 ----------------------------------------

const seatPhoneStore = useSeatPhoneStore()
const { humanMachineSpeechCraftId } = storeToRefs(seatPhoneStore)
const seatInfoStore = useSeatInfoStore()
const { seatStatus } = storeToRefs(seatInfoStore)
const interactionStore = useInteractionStore()
const { humanMachineActiveTab } = storeToRefs(interactionStore)

const props = defineProps<{
  aiRecord: TaskCallRecordItem,
}>()
const emits = defineEmits([
  'update:humanRecord'
])

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 标签页 开始 ----------------------------------------

// 标签页名称合集
const tabList: WorkbenchTabEnum[] = [
  WorkbenchTabEnum['AI通话记录'],
  WorkbenchTabEnum['客户状态记录'],
]

/**
 * 标签页组件 更新标签页
 * @param val 新标签页名称
 */
const onUpdateTab = (val: WorkbenchTabEnum) => {
  interactionStore.updateHumanMachineActiveTab(val)
}

// ---------------------------------------- 标签页 结束 ----------------------------------------

// ---------------------------------------- AI通话记录 开始 ----------------------------------------

// AI通话记录 默认值
class AiRecord {
  // 客户名称
  name: string = ''
  // 名单编号
  id: string = ''
  // 省
  province: string = ''
  // 市
  city: string = ''
  // 号码
  recordId: string = ''
  // 任务名称
  taskName: string = ''
  // 执行话术
  scriptName: string = ''
  // 意向分类
  intentionClass: string = ''
  // 标签
  intentionLabels: string = ''
}

// AI通话记录
const aiRecord = ref<AiRecord>(new AiRecord())

watch(seatStatus, async (val: SeatStatusEnum) => {
  // 人机协同 弹窗 监听 介入 话后处理
  if (
    val === SeatStatusEnum.HUMAN_MACHINE_WINDOW
    || val === SeatStatusEnum.HUMAN_MACHINE_LISTEN
    || val === SeatStatusEnum.HUMAN_MACHINE_DIALING
    || val === SeatStatusEnum.HUMAN_MACHINE_POSTING
  ) {
    await updateHumanRecordAvailableData()
  }
})
// 工作台父组件更新AI通话记录时，当前子组件同步更新
watch(props, (val) => {
  aiRecord.value = JSON.parse(JSON.stringify(val.aiRecord))
})

// ---------------------------------------- AI通话记录 结束 ----------------------------------------

// ---------------------------------------- 人工通话记录 开始 ----------------------------------------

// 人工通话记录 默认值
class ManualRecord {
  manualIntention: IntentionType = {
    intentionType: '',
    intentionName: '',
  }
  manualLabels: LabelItem[] = []
}

// 人工通话记录
const manualRecord = ref<ManualRecord>(new ManualRecord())

// 全部意向分类
const intentionAllList = ref<IntentionType[]>([])
// 全部标签
const labelAllList = ref<LabelItem[]>([])
// 可选标签
const labelLeftList = ref<LabelItem[]>([])

// 人工通话记录，组件数据转换成接口数据
watch(manualRecord, (val: ManualRecord) => {
  const data: SeatCallParam = {
    intentionClass: val.manualIntention.intentionType ?? undefined,
  }
  if (val.manualLabels?.length) {
    const tagNameList = val.manualLabels.map((label: LabelItem) => {
      return label?.labelName ?? ''
    })
    const tagNameSet = new Set(tagNameList)
    tagNameSet.delete('')
    data.intentionTags = Array.from(tagNameSet).join(',')
  }
  emits('update:humanRecord', data)
}, { deep: true })

/**
 * 更新获取当前话术的意向等级和标签
 */
const updateHumanRecordAvailableData = async () => {
  intentionAllList.value = []
  labelAllList.value = []

  console.log('更新获取当前话术的意向等级和标签 人机协同 线索 话术ID', humanMachineSpeechCraftId.value)
  if (!humanMachineSpeechCraftId.value) {
    console.warn('更新获取当前话术的意向等级和标签 人机协同 线索 话术ID 不存在')
    return
  }

  try {
    const res = <SeatCallResponse>await seatWorkbenchCallModel.getHumanRecordAvailableData({
      scriptId: humanMachineSpeechCraftId.value ?? null
    })
    intentionAllList.value = Array.isArray(res?.aiIntentionTypes) ? res?.aiIntentionTypes : []
    labelAllList.value = Array.isArray(res?.aiLabels) ? res?.aiLabels : []
    // 更新可选标签
    updateLabelLeftList()
  } catch (e) {
  }
}
/**
 * 获取可选标签
 */
const updateLabelLeftList = () => {
  // 未选中任何标签，就照搬全部标签
  if (!manualRecord.value.manualLabels.length) {
    labelLeftList.value = JSON.parse(JSON.stringify(labelAllList.value))
    return
  }

  // 剔除已添加的人工标签
  labelLeftList.value = labelAllList.value.filter((item: LabelItem) => {
    return !manualRecord.value.manualLabels.find((selectedItem: LabelItem) => {
      return selectedItem.labelName === item.labelName
    })
  })
}
/**
 * 删除人工标签
 * @param {LabelItem} labelItem 人工标签信息
 */
const deleteManualLabel = (labelItem: LabelItem) => {
  // 深拷贝
  const label = JSON.parse(JSON.stringify(labelItem))

  // 找到标签索引
  const index = manualRecord.value.manualLabels.findIndex((item: LabelItem) => {
    return item.id === label.id
  })
  // 删除
  if (index > -1) {
    manualRecord.value.manualLabels.splice(index, 1)
  }

  // 更新
  updateLabelLeftList()
}
/**
 * 添加人工标签
 * @param {LabelItem} labelItem 人工标签信息
 */
const addManualLabel = (labelItem: LabelItem) => {
  // 深拷贝
  const label = JSON.parse(JSON.stringify(labelItem))

  // 将选中的标签添加进去
  if (manualRecord.value.manualLabels?.length) {
    manualRecord.value.manualLabels.push(label)
  } else {
    manualRecord.value.manualLabels = [label]
  }

  // 更新
  updateLabelLeftList()
}

// ---------------------------------------- 人工通话记录结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
.block {
  overflow: hidden;
  height: calc(100% - 37px);
}
.tab-box {
  height: 37px;
  /* 右侧模块 标签卡 */
  &.workbench-tab {
    :deep(.normal-tab) {
      width: 118px;
      height: 32px;
      font-size: 14px;
      line-height: 28px;
    }
  }
}
.info-block-title {
  margin: 8px 0;
  color: #313233;
  font-size: 13px;
  font-weight: 600;
  line-height: 20px;
}
.info-item {
  margin: 0 0 12px;
}
.info-text-bold {
  font-weight: bold;
}
.list-item-tag {
  height: auto;
  margin: 6px 6px 0 0;
  padding: 6px;
  font-size: 13px;
  line-height: 20px;
  white-space: break-spaces;
  user-select: none;
}
:deep(.el-tag__close) {
  flex: none;
}
</style>
